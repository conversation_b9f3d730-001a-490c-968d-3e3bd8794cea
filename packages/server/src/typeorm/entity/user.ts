import { z } from '@cfe-node/koa-swagger-decorator'
/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2023-12-20 13:07:30
 * @LastEditTime: 2025-07-11 13:09:36
 * @LastEditors: shaojun
 * @Description: 用户实体 - MySQL 版本 (优化版)
 */
import { Column, Entity, Index, ManyToOne, OneToMany } from 'typeorm'
import { RoleEnum } from '../enums'
import { BaseEntity } from './common'

@Entity('users')
@Index(['createdAt']) // 时间索引，用于排序和范围查询
@Index(['company', 'career']) // 复合索引，用于职业相关查询
export class User extends BaseEntity {
  constructor(username?: string, password?: string, email?: string) {
    super()
    if (username) {
      this.username = username
    }
    if (password) {
      this.password = password
    }
    if (email) {
      this.email = email
    }
    // 初始化默认值
    this.userAvatar = ''
    this.token = ''
    this.apiToken = null
    this.mobile = ''
    this.qq = ''
    this.company = ''
    this.career = ''
  }

  @Column({
    type: 'varchar',
    length: 50,
    unique: true,
    comment: '用户名',
  })
  username!: string

  @Column({
    type: 'varchar',
    length: 500,
    default: '',
    comment: '用户头像URL',
  })
  userAvatar: string

  @Column({
    type: 'text',
    nullable: true,
    comment: '头像历史记录 (JSON 数组)',
  })
  userAvatarHistory?: string

  @Column({
    type: 'varchar',
    length: 255,
    select: false, // 查询时默认不返回密码
    comment: '密码 (加密存储)',
  })
  password!: string

  @Column({
    type: 'varchar',
    length: 100,
    unique: true,
    comment: '邮箱地址',
  })
  email!: string

  @Column({
    type: 'varchar',
    length: 500,
    default: '',
    comment: '认证令牌',
  })
  token: string

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: 'API 访问令牌',
  })
  apiToken: string | null

  @Column({
    type: 'varchar',
    length: 20,
    default: '',
    comment: '手机号码',
  })
  mobile: string

  @Column({
    type: 'varchar',
    length: 20,
    default: '',
    comment: 'QQ 号码',
  })
  qq: string

  @Column({
    type: 'varchar',
    length: 100,
    default: '',
    comment: '公司名称',
  })
  company: string

  @Column({
    type: 'varchar',
    length: 100,
    default: '',
    comment: '职业/职位',
  })
  career: string

  // 优化关系映射：使用懒加载避免N+1查询问题
  @OneToMany(() => UserTeam, (userTeam) => userTeam.user, {
    lazy: true, // 懒加载，按需获取
    cascade: true, // 级联操作
  })
  userTeams!: Promise<UserTeam[]>

  // 便捷方法：获取用户头像历史
  getUserAvatarHistory(): string[] {
    try {
      return this.userAvatarHistory ? JSON.parse(this.userAvatarHistory) : []
    } catch {
      return []
    }
  }

  // 便捷方法：设置用户头像历史
  setUserAvatarHistory(history: string[]): void {
    this.userAvatarHistory = JSON.stringify(history)
  }

  // 便捷方法：添加头像到历史记录
  addAvatarToHistory(avatarUrl: string): void {
    const history = this.getUserAvatarHistory()
    if (!history.includes(avatarUrl)) {
      history.unshift(avatarUrl)
      // 限制历史记录数量为10个
      if (history.length > 10) {
        history.splice(10)
      }
      this.setUserAvatarHistory(history)
    }
  }

  // 便捷方法：验证邮箱格式
  isValidEmail(): boolean {
    const emailRegex = /^[^\s@]+@[^\s@][^\s.@]*\.[^\s@]+$/
    return emailRegex.test(this.email)
  }

  // 便捷方法：验证手机号格式
  isValidMobile(): boolean {
    const mobileRegex = /^1[3-9]\d{9}$/
    return this.mobile ? mobileRegex.test(this.mobile) : true
  }

  // 便捷方法：获取用户显示名称
  getDisplayName(): string {
    return this.username || this.email.split('@')[0]
  }

  // 便捷方法：检查用户是否完善了个人信息
  isProfileComplete(): boolean {
    return !!(this.username && this.email && this.userAvatar)
  }
}

/**
 * 用户团队关系实体 (中间表) - 优化版
 */
@Entity('user_teams')
@Index(['userId', 'teamId'], { unique: true }) // 复合唯一索引
@Index(['role']) // 角色索引，用于权限查询
@Index(['createdAt']) // 时间索引，用于加入时间排序
export class UserTeam extends BaseEntity {
  constructor(userId?: number, teamId?: number, role?: RoleEnum) {
    super()
    if (userId) {
      this.userId = userId
    }
    if (teamId) {
      this.teamId = teamId
    }
    if (role) {
      this.role = role
    } else {
      this.role = RoleEnum.guest // 默认角色
    }
  }

  @Column({
    type: 'int',
    comment: '用户ID',
  })
  userId!: number

  @Column({
    type: 'int',
    comment: '团队ID',
  })
  teamId!: number

  @Column({
    type: 'enum',
    enum: RoleEnum,
    default: RoleEnum.guest,
    comment: '用户在团队中的角色',
  })
  role: RoleEnum

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '团队名称 (冗余字段，便于查询)',
  })
  teamName?: string

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: '团队图标 (冗余字段，便于查询)',
  })
  teamIcon?: string

  // 优化关系映射：使用 ManyToOne 而不是 OneToMany
  @ManyToOne(() => User, (user) => user.userTeams)
  user!: User

  @ManyToOne('Team', 'userTeams')
  team!: any // 使用 any 类型避免循环依赖

  // 便捷方法：检查是否为管理员角色
  isAdmin(): boolean {
    return this.role === RoleEnum.owner || this.role === RoleEnum.manager
  }

  // 便捷方法：检查是否为所有者
  isOwner(): boolean {
    return this.role === RoleEnum.owner
  }

  // 便捷方法：获取角色权重
  getRoleWeight(): number {
    const weights = {
      [RoleEnum.owner]: 100,
      [RoleEnum.manager]: 10,
      [RoleEnum.guest]: 1,
    }
    return weights[this.role] || 0
  }
}

// Schema 定义 =======================================

/**
 * 用户团队关系 Schema
 */
export const UserTeamSchema = z.object({
  id: z.number().openapi({ description: '关系ID' }),
  userId: z.number().openapi({ description: '用户ID' }),
  teamId: z.number().openapi({ description: '团队ID' }),
  teamName: z.string().optional().openapi({ description: '团队名称' }),
  teamIcon: z.string().optional().openapi({ description: '团队图标' }),
  role: z.nativeEnum(RoleEnum).openapi({
    description: '用户角色',
    example: RoleEnum.guest,
  }),
  createdAt: z.date().openapi({ description: '加入时间' }),
})

/**
 * 用户信息 Schema
 */
export const UserInfo = z.object({
  id: z.number().openapi({ description: '用户ID' }),
  username: z.string().openapi({
    description: '用户名',
    example: 'john_doe',
  }),
  userAvatar: z.string().openapi({
    description: '用户头像URL',
    example: 'https://example.com/avatar.jpg',
  }),
  userAvatarHistory: z.array(z.string()).optional().openapi({
    description: '头像历史记录',
  }),
  email: z.string().email().openapi({
    description: '邮箱地址',
    example: '<EMAIL>',
  }),
  token: z.string().openapi({ description: '认证令牌' }),
  apiToken: z.string().nullable().openapi({ description: 'API 访问令牌' }),
  mobile: z.string().openapi({
    description: '手机号码',
    example: '13800138000',
  }),
  qq: z.string().openapi({
    description: 'QQ 号码',
    example: '*********',
  }),
  company: z.string().openapi({
    description: '公司名称',
    example: 'Example Corp',
  }),
  career: z.string().openapi({
    description: '职业/职位',
    example: '前端开发工程师',
  }),
  userTeams: z.array(UserTeamSchema).optional().openapi({
    description: '用户的团队关系',
  }),
  createdAt: z.date().openapi({ description: '创建时间' }),
  updatedAt: z.date().openapi({ description: '更新时间' }),
}).openapi('UserInfo')

/**
 * 用户创建 Schema
 */
export const CreateUserSchema = z.object({
  username: z.string().min(3).max(50).openapi({
    description: '用户名，3-50字符',
    example: 'john_doe',
  }),
  password: z.string().min(6).max(100).openapi({
    description: '密码，6-100字符',
    example: 'password123',
  }),
  email: z.string().email().openapi({
    description: '邮箱地址',
    example: '<EMAIL>',
  }),
  mobile: z.string().optional().openapi({
    description: '手机号码',
    example: '13800138000',
  }),
  company: z.string().optional().openapi({
    description: '公司名称',
    example: 'Example Corp',
  }),
  career: z.string().optional().openapi({
    description: '职业/职位',
    example: '前端开发工程师',
  }),
})

/**
 * 用户更新 Schema
 */
export const UpdateUserSchema = z.object({
  username: z.string().min(3).max(50).optional(),
  userAvatar: z.string().url().optional(),
  mobile: z.string().optional(),
  qq: z.string().optional(),
  company: z.string().optional(),
  career: z.string().optional(),
})

/**
 * 用户登录 Schema
 */
export const LoginUserSchema = z.object({
  username: z.string().openapi({
    description: '用户名或邮箱',
    example: 'john_doe',
  }),
  password: z.string().openapi({
    description: '密码',
    example: 'password123',
  }),
})
