import { z } from '@cfe-node/koa-swagger-decorator'
/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2024-04-22 13:17:22
 * @LastEditTime: 2025-07-21 17:38:24
 * @LastEditors: shaojun
 * @Description: 消息实体 - MySQL 版本
 */
import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm'
import { BaseEntity } from './common'

@Entity('messages')
@Index(['senderId'])
@Index(['receiverId'])
@Index(['category'])
@Index(['status'])
@Index(['sendAt'])
export class Message extends BaseEntity {
  constructor(category?: string, content?: string, senderId?: number, receiverId?: number) {
    super()
    if (category) {
      this.category = category
    }
    if (content) {
      this.content = content
    }
    if (senderId) {
      this.senderId = senderId
    }
    // 初始化必需属性
    this.receiverId = receiverId || 0
    this.status = 'pending'
    this.sendAt = new Date()
  }

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '消息类型 (INVITE/SYSTEM/NOTICE)',
  })
  category?: string

  @Column({
    type: 'text',
    nullable: true,
    comment: '消息内容',
  })
  content?: string

  @Column({
    type: 'int',
    nullable: true,
    comment: '发送者用户ID',
  })
  senderId?: number

  @Column({
    type: 'int',
    comment: '接收者用户ID',
  })
  receiverId: number

  @Column({
    type: 'datetime',
    comment: '发送时间',
  })
  sendAt: Date

  @Column({
    type: 'varchar',
    length: 20,
    default: 'pending',
    comment: '消息状态 (pending/sent/read/deleted)',
  })
  status: string

  @Column({
    type: 'text',
    nullable: true,
    comment: '消息附加数据 (JSON格式)',
  })
  data?: string

  // 关系映射：发送者
  @ManyToOne('User', { lazy: true })
  @JoinColumn({ name: 'senderId' })
  sender!: Promise<any> // User类型

  // 关系映射：接收者
  @ManyToOne('User', { lazy: true })
  @JoinColumn({ name: 'receiverId' })
  receiver!: Promise<any> // User类型

  // 便捷方法：获取消息数据
  getMessageData(): Record<string, any> {
    try {
      return this.data ? JSON.parse(this.data) : {}
    } catch {
      return {}
    }
  }

  // 便捷方法：设置消息数据
  setMessageData(data: Record<string, any>): void {
    this.data = JSON.stringify(data)
  }

  // 便捷方法：标记为已读
  markAsRead(): void {
    this.status = 'read'
  }

  // 便捷方法：检查是否为邀请消息
  isInviteMessage(): boolean {
    return this.category === 'INVITE'
  }

  // 便捷方法：检查是否已读
  isRead(): boolean {
    return this.status === 'read'
  }
}

/**
 * 消息信息 Schema
 */
export const MessageInfo = z.object({
  id: z.number().openapi({ description: '消息ID' }),
  category: z.string().optional().openapi({
    description: '消息类型',
    example: 'INVITE',
  }),
  content: z.string().optional().openapi({ description: '消息内容' }),
  senderId: z.number().optional().openapi({ description: '发送者ID' }),
  receiverId: z.number().openapi({ description: '接收者ID' }),
  sendAt: z.date().openapi({ description: '发送时间' }),
  status: z.string().openapi({
    description: '消息状态',
    example: 'pending',
  }),
  data: z.string().optional().openapi({ description: '附加数据' }),
  createdAt: z.date().openapi({ description: '创建时间' }),
  updatedAt: z.date().openapi({ description: '更新时间' }),
})

/**
 * 创建消息 Schema
 */
export const CreateMessageSchema = z.object({
  category: z.string().openapi({ description: '消息类型' }),
  content: z.string().openapi({ description: '消息内容' }),
  receiverId: z.number().openapi({ description: '接收者ID' }),
  data: z.record(z.string(), z.any()).optional().openapi({ description: '附加数据' }),
})

/**
 * 更新消息状态 Schema
 */
export const UpdateMessageStatusSchema = z.object({
  status: z.enum(['pending', 'sent', 'read', 'deleted']).openapi({
    description: '消息状态',
  }),
})

/**
 * 消息查询 Schema
 */
export const MessageQuerySchema = z.object({
  category: z.string().optional().openapi({ description: '消息类型' }),
  status: z.string().optional().openapi({ description: '消息状态' }),
  senderId: z.number().optional().openapi({ description: '发送者ID' }),
  receiverId: z.number().optional().openapi({ description: '接收者ID' }),
  startDate: z.string().datetime().optional().openapi({ description: '开始时间' }),
  endDate: z.string().datetime().optional().openapi({ description: '结束时间' }),
})
