import { z } from '@cfe-node/koa-swagger-decorator'
/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2024-04-22 11:41:13
 * @LastEditTime: 2025-07-11 13:13:05
 * @LastEditors: shaojun
 * @Description: 下载记录实体 - MySQL 版本
 */
import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm'
import { BaseEntity } from './common'

@Entity('downloads')
@Index(['appId'])
@Index(['versionId'])
@Index(['userId'])
@Index(['createdAt'])
@Index(['clientIp'])
export class Download extends BaseEntity {
  constructor(appId?: number, versionId?: number, userId?: number) {
    super()
    // 初始化必需属性
    this.appId = appId || 0
    this.downloadType = 'web'
    if (versionId) {
      this.versionId = versionId
    }
    if (userId) {
      this.userId = userId
    }
  }

  @Column({
    type: 'int',
    comment: '应用ID',
  })
  appId: number

  @Column({
    type: 'int',
    nullable: true,
    comment: '版本ID',
  })
  versionId?: number

  @Column({
    type: 'int',
    nullable: true,
    comment: '用户ID',
  })
  userId?: number

  @Column({
    type: 'varchar',
    length: 45,
    nullable: true,
    comment: '客户端IP地址',
  })
  clientIp?: string

  @Column({
    type: 'text',
    nullable: true,
    comment: '用户代理字符串',
  })
  userAgent?: string

  @Column({
    type: 'varchar',
    length: 50,
    default: 'web',
    comment: '下载类型 (web/api/direct)',
  })
  downloadType: string

  @Column({
    type: 'text',
    nullable: true,
    comment: '下载备注',
  })
  remark?: string

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: '下载页面图片',
  })
  image?: string

  @Column({
    type: 'text',
    nullable: true,
    comment: '下载参数 (JSON)',
  })
  param?: string

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: '下载页面URL',
  })
  page?: string

  // 关系映射：所属应用
  @ManyToOne('App', 'downloads')
  @JoinColumn({ name: 'appId' })
  app: any // App类型

  // 关系映射：下载的版本
  @ManyToOne('Version', 'downloads')
  @JoinColumn({ name: 'versionId' })
  version: any // Version类型

  // 关系映射：下载用户
  @ManyToOne('User', { lazy: true })
  @JoinColumn({ name: 'userId' })
  user: Promise<any> // User类型

  // 便捷方法：获取下载参数
  getDownloadParams(): Record<string, any> {
    try {
      return this.param ? JSON.parse(this.param) : {}
    } catch {
      return {}
    }
  }

  // 便捷方法：设置下载参数
  setDownloadParams(params: Record<string, any>): void {
    this.param = JSON.stringify(params)
  }

  // 便捷方法：检查是否为移动端下载
  isMobileDownload(): boolean {
    if (!this.userAgent) {
      return false
    }
    return /Mobile|Android|iPhone|iPad/.test(this.userAgent)
  }
}

/**
 * 下载信息 Schema
 */
export const DownloadInfo = z.object({
  id: z.number().openapi({ description: '下载记录ID' }),
  appId: z.number().openapi({ description: '应用ID' }),
  versionId: z.number().optional().openapi({ description: '版本ID' }),
  userId: z.number().optional().openapi({ description: '用户ID' }),
  clientIp: z.string().optional().openapi({
    description: '客户端IP',
    example: '***********',
  }),
  userAgent: z.string().optional().openapi({ description: '用户代理' }),
  downloadType: z.string().openapi({
    description: '下载类型',
    example: 'web',
  }),
  remark: z.string().optional().openapi({ description: '备注' }),
  image: z.string().optional().openapi({ description: '下载页面图片' }),
  param: z.string().optional().openapi({ description: '下载参数' }),
  page: z.string().optional().openapi({ description: '下载页面URL' }),
  createdAt: z.date().openapi({ description: '下载时间' }),
  updatedAt: z.date().openapi({ description: '更新时间' }),
})

/**
 * 创建下载记录 Schema
 */
export const CreateDownloadSchema = z.object({
  appId: z.number().openapi({ description: '应用ID' }),
  versionId: z.number().optional().openapi({ description: '版本ID' }),
  downloadType: z.string().optional().openapi({ description: '下载类型' }),
  remark: z.string().optional().openapi({ description: '备注' }),
})

/**
 * 下载统计 Schema
 */
export const DownloadStatSchema = z.object({
  appId: z.number().openapi({ description: '应用ID' }),
  totalDownloads: z.number().openapi({ description: '总下载次数' }),
  todayDownloads: z.number().openapi({ description: '今日下载次数' }),
  downloadsByType: z.record(z.string(), z.number()).openapi({ description: '按类型统计' }),
  downloadTrend: z.array(z.object({
    date: z.date(),
    count: z.number(),
  })).optional().openapi({ description: '下载趋势' }),
})
