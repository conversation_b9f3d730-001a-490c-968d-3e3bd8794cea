import { z } from '@cfe-node/koa-swagger-decorator'
/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2023-12-20 13:07:30
 * @LastEditTime: 2025-07-11 13:12:36
 * @LastEditors: shaojun
 * @Description: 版本实体 - MySQL 版本 (优化版)
 */
import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  UpdateDateColumn,
} from 'typeorm'
import { UpdateModeEnum } from '../enums'
import { BaseEntity } from './common'

@Entity('versions')
@Index(['appId']) // 应用索引，查询应用的版本
@Index(['bundleId']) // 包标识符索引
@Index(['versionStr', 'versionCode']) // 复合索引，版本号查询
@Index(['uploaderId']) // 上传者索引
@Index(['active', 'released']) // 复合索引，查询活跃已发布版本
@Index(['appId', 'versionStr']) // 复合索引，应用版本查询
@Index(['uploadAt']) // 上传时间索引，用于排序
@Index(['downloadCount']) // 下载次数索引，热门版本排序
@Index(['appId', 'active', 'released']) // 复合索引，查询应用的可用版本
export class Version extends BaseEntity {
  constructor() {
    super()
    this.active = true
    this.downloadCount = 0
    this.showOnDownloadPage = true
    this.hidden = false
    this.released = false
    this.updateMode = UpdateModeEnum.normal
    this.size = 0
    this.sortWeight = 0
    this.uploadAt = new Date()
    this.status = 'draft'
  }

  @Column({
    type: 'int',
    comment: '应用ID',
  })
  appId!: number

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '包标识符',
  })
  bundleId?: string

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: '版本图标URL',
  })
  icon?: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '版本字符串',
  })
  versionStr?: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '版本代码',
  })
  versionCode?: string

  @UpdateDateColumn()
  uploadAt: Date

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '上传者名称',
  })
  uploader?: string

  @Column({
    type: 'int',
    nullable: true,
    comment: '上传者用户ID',
  })
  uploaderId?: number

  @Column({
    type: 'bigint',
    default: 0,
    comment: '文件大小 (字节)',
  })
  size: number

  @Column({
    type: 'boolean',
    default: true,
    comment: '是否激活',
  })
  active: boolean

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: '下载URL',
  })
  downloadUrl?: string

  @Column({
    type: 'bigint',
    default: 0,
    comment: '下载次数',
  })
  downloadCount: number

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: '文件下载URL',
  })
  fileDownloadUrl?: string

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: '安装URL',
  })
  installUrl?: string

  @Column({
    type: 'boolean',
    default: true,
    comment: '是否在下载页面显示',
  })
  showOnDownloadPage: boolean

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '应用等级',
  })
  appLevel?: string

  @Column({
    type: 'text',
    nullable: true,
    comment: '更新日志',
  })
  changelog?: string

  @Column({
    type: 'varchar',
    length: 64,
    nullable: true,
    comment: '文件MD5值',
  })
  md5?: string

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否隐藏',
  })
  hidden: boolean

  @Column({
    type: 'enum',
    enum: UpdateModeEnum,
    default: UpdateModeEnum.normal,
    comment: '更新模式',
  })
  updateMode: UpdateModeEnum

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否已发布',
  })
  released: boolean

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '版本标签',
  })
  versionTag?: string

  @Column({
    type: 'text',
    nullable: true,
    comment: '版本备注',
  })
  versionNote?: string

  @Column({
    type: 'int',
    default: 0,
    comment: '版本排序权重',
  })
  sortWeight: number

  @Column({
    type: 'varchar',
    length: 50,
    default: 'draft',
    comment: '版本状态 (draft/testing/released/archived)',
  })
  status: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '最低系统版本要求',
  })
  minOsVersion?: string

  @Column({
    type: 'varchar',
    length: 200,
    nullable: true,
    comment: '支持的设备类型 (JSON数组)',
  })
  supportedDevices?: string

  @Column({
    type: 'json',
    nullable: true,
    comment: '版本元数据 (JSON对象)',
  })
  metadata?: string

  @Column({
    type: 'datetime',
    nullable: true,
    comment: '发布时间',
  })
  releasedAt?: Date

  @Column({
    type: 'int',
    nullable: true,
    comment: '版本序号 (自动递增)',
  })
  buildNumber?: number

  // 优化关系映射：所属应用
  @ManyToOne('App', 'versions')
  @JoinColumn({ name: 'appId' })
  app!: any // App类型

  // 优化关系映射：上传者
  @ManyToOne('User', { lazy: true })
  @JoinColumn({ name: 'uploaderId' })
  uploaderUser!: Promise<any> // User类型

  // 优化关系映射：下载记录
  @OneToMany('Download', 'version', { lazy: true })
  downloads!: Promise<any[]> // Download[]类型

  // 便捷方法：增加下载次数
  incrementDownloadCount(count: number = 1): void {
    this.downloadCount += count
  }

  // 便捷方法：检查是否可下载
  isDownloadable(): boolean {
    return this.active && !this.hidden && !!this.downloadUrl
  }

  // 便捷方法：获取文件大小的可读格式
  getFormattedSize(): string {
    const units = ['B', 'KB', 'MB', 'GB']
    let size = this.size
    let unitIndex = 0

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`
  }

  // 便捷方法：检查版本是否为最新
  async isLatestVersion(): Promise<boolean> {
    // 这里需要查询同应用下的其他版本进行比较
    // 实际实现需要在 service 层完成
    return this.released
  }

  // 便捷方法：检查是否为预发布版本
  isPreRelease(): boolean {
    return this.status === 'testing' || this.status === 'draft'
  }

  // 便捷方法：检查是否已发布
  isReleased(): boolean {
    return this.released && this.status === 'released'
  }

  // 便捷方法：发布版本
  release(): void {
    this.released = true
    this.status = 'released'
    this.releasedAt = new Date()
  }

  // 便捷方法：撤回发布
  unrelease(): void {
    this.released = false
    this.status = 'draft'
    this.releasedAt = undefined
  }

  // 便捷方法：隐藏版本
  hide(): void {
    this.hidden = true
    this.showOnDownloadPage = false
  }

  // 便捷方法：显示版本
  show(): void {
    this.hidden = false
    this.showOnDownloadPage = true
  }

  // 便捷方法：停用版本
  deactivate(): void {
    this.active = false
    this.hidden = true
  }

  // 便捷方法：激活版本
  activate(): void {
    this.active = true
    this.hidden = false
  }

  // 便捷方法：归档版本
  archive(): void {
    this.status = 'archived'
    this.active = false
    this.hidden = true
  }

  // 便捷方法：获取支持的设备列表
  getSupportedDevices(): string[] {
    try {
      return this.supportedDevices ? JSON.parse(this.supportedDevices) : []
    } catch {
      return []
    }
  }

  // 便捷方法：设置支持的设备列表
  setSupportedDevices(devices: string[]): void {
    this.supportedDevices = JSON.stringify(devices)
  }

  // 便捷方法：获取元数据
  getMetadata(): Record<string, any> {
    try {
      return this.metadata ? JSON.parse(this.metadata) : {}
    } catch {
      return {}
    }
  }

  // 便捷方法：设置元数据
  setMetadata(metadata: Record<string, any>): void {
    this.metadata = JSON.stringify(metadata)
  }

  // 便捷方法：比较版本号
  compareVersion(otherVersion: string): number {
    const thisVersion = this.versionStr || '0.0.0'
    const parts1 = thisVersion.split('.').map(Number)
    const parts2 = otherVersion.split('.').map(Number)

    const maxLength = Math.max(parts1.length, parts2.length)

    for (let i = 0; i < maxLength; i++) {
      const part1 = parts1[i] || 0
      const part2 = parts2[i] || 0

      if (part1 > part2) {
        return 1
      }
      if (part1 < part2) {
        return -1
      }
    }

    return 0
  }

  // 便捷方法：检查是否需要强制更新
  isForceUpdate(): boolean {
    return this.updateMode === UpdateModeEnum.force
  }

  // 便捷方法：检查是否为静默更新
  isSilentUpdate(): boolean {
    return this.updateMode === UpdateModeEnum.silent
  }

  // 便捷方法：获取版本显示名称
  getDisplayName(): string {
    if (this.versionTag) {
      return `${this.versionStr || 'Unknown'} (${this.versionTag})`
    }
    return this.versionStr || `Build ${this.buildNumber || this.id}`
  }

  // 便捷方法：设置构建号
  setBuildNumber(number: number): void {
    this.buildNumber = number
  }

  // 便捷方法：检查文件完整性
  verifyIntegrity(actualMd5: string): boolean {
    return this.md5 === actualMd5
  }
}

// Schema 定义 =======================================

/**
 * 版本信息 Schema (优化版)
 */
export const VersionInfo = z.object({
  id: z.number().openapi({ description: '版本ID' }),
  appId: z.number().openapi({ description: '应用ID' }),
  bundleId: z.string().optional().openapi({ description: '包标识符' }),
  icon: z.string().optional().openapi({ description: '版本图标URL' }),
  versionStr: z.string().optional().openapi({
    description: '版本字符串',
    example: '1.0.0',
  }),
  versionCode: z.string().optional().openapi({
    description: '版本代码',
    example: '100',
  }),
  uploadAt: z.date().openapi({ description: '上传时间' }),
  uploader: z.string().optional().openapi({ description: '上传者名称' }),
  uploaderId: z.number().optional().openapi({ description: '上传者ID' }),
  size: z.number().openapi({ description: '文件大小(字节)' }),
  active: z.boolean().openapi({ description: '是否激活' }),
  downloadUrl: z.string().optional().openapi({ description: '下载URL' }),
  downloadCount: z.number().openapi({ description: '下载次数' }),
  fileDownloadUrl: z.string().optional().openapi({ description: '文件下载URL' }),
  installUrl: z.string().optional().openapi({ description: '安装URL' }),
  showOnDownloadPage: z.boolean().openapi({ description: '是否在下载页显示' }),
  appLevel: z.string().optional().openapi({ description: '应用等级' }),
  changelog: z.string().optional().openapi({ description: '更新日志' }),
  md5: z.string().optional().openapi({ description: '文件MD5值' }),
  hidden: z.boolean().openapi({ description: '是否隐藏' }),
  updateMode: z.nativeEnum(UpdateModeEnum).openapi({ description: '更新模式' }),
  released: z.boolean().openapi({ description: '是否已发布' }),
  versionTag: z.string().optional().openapi({ description: '版本标签' }),
  versionNote: z.string().optional().openapi({ description: '版本备注' }),
  sortWeight: z.number().openapi({ description: '排序权重' }),
  status: z.string().openapi({ description: '版本状态' }),
  minOsVersion: z.string().optional().openapi({ description: '最低系统版本' }),
  supportedDevices: z.array(z.string()).optional().openapi({ description: '支持设备' }),
  metadata: z.record(z.string(), z.unknown()).optional().openapi({ description: '版本元数据' }),
  releasedAt: z.date().optional().openapi({ description: '发布时间' }),
  buildNumber: z.number().optional().openapi({ description: '构建号' }),
  createdAt: z.date().openapi({ description: '创建时间' }),
  updatedAt: z.date().openapi({ description: '更新时间' }),
})

/**
 * 创建版本 Schema (优化版)
 */
export const CreateVersionSchema = z.object({
  appId: z.number().openapi({ description: '应用ID' }),
  versionStr: z.string().openapi({ description: '版本字符串' }),
  versionCode: z.string().openapi({ description: '版本代码' }),
  changelog: z.string().optional().openapi({ description: '更新日志' }),
  appLevel: z.string().optional().openapi({ description: '应用等级' }),
  updateMode: z.nativeEnum(UpdateModeEnum).optional().openapi({
    description: '更新模式',
    default: UpdateModeEnum.normal,
  }),
  versionTag: z.string().optional().openapi({ description: '版本标签' }),
  versionNote: z.string().optional().openapi({ description: '版本备注' }),
  minOsVersion: z.string().optional().openapi({ description: '最低系统版本' }),
  supportedDevices: z.array(z.string()).optional().openapi({ description: '支持设备' }),
  metadata: z.record(z.string(), z.any()).optional().openapi({ description: '版本元数据' }),
})

/**
 * 更新版本 Schema (优化版)
 */
export const UpdateVersionSchema = z.object({
  versionStr: z.string().optional(),
  versionCode: z.string().optional(),
  changelog: z.string().optional(),
  active: z.boolean().optional(),
  hidden: z.boolean().optional(),
  released: z.boolean().optional(),
  showOnDownloadPage: z.boolean().optional(),
  updateMode: z.nativeEnum(UpdateModeEnum).optional(),
  versionTag: z.string().optional(),
  versionNote: z.string().optional(),
  sortWeight: z.number().optional(),
  status: z.enum(['draft', 'testing', 'released', 'archived']).optional(),
  minOsVersion: z.string().optional(),
  supportedDevices: z.array(z.string()).optional(),
  metadata: z.record(z.string(), z.unknown()).optional(),
})

/**
 * 版本发布 Schema (优化版)
 */
export const ReleaseVersionSchema = z.object({
  versionId: z.number().openapi({ description: '版本ID' }),
  isGrayRelease: z.boolean().optional().openapi({
    description: '是否为灰度发布',
    default: false,
  }),
  releaseNote: z.string().optional().openapi({ description: '发布说明' }),
  updateMode: z.nativeEnum(UpdateModeEnum).optional().openapi({
    description: '更新模式',
    default: UpdateModeEnum.normal,
  }),
  sortWeight: z.number().optional().openapi({ description: '排序权重' }),
})

/**
 * 版本统计 Schema (优化版)
 */
export const VersionStatSchema = z.object({
  versionId: z.number().openapi({ description: '版本ID' }),
  downloadCount: z.number().openapi({ description: '下载次数' }),
  downloadTrend: z.array(z.object({
    date: z.date(),
    count: z.number(),
  })).optional().openapi({ description: '下载趋势' }),
  conversionRate: z.number().optional().openapi({ description: '转化率' }),
  deviceDistribution: z.record(z.string(), z.number()).optional().openapi({ description: '设备分布' }),
  osVersionDistribution: z.record(z.string(), z.number()).optional().openapi({ description: '系统版本分布' }),
})

/**
 * 版本比较 Schema
 */
export const VersionCompareSchema = z.object({
  versionId1: z.number().openapi({ description: '版本1 ID' }),
  versionId2: z.number().openapi({ description: '版本2 ID' }),
  compareFields: z.array(z.string()).optional().openapi({
    description: '比较字段',
    default: ['size', 'downloadCount', 'changelog']
  }),
})
