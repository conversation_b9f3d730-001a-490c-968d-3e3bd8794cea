import { z } from '@cfe-node/koa-swagger-decorator'
/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2024-04-17 13:38:42
 * @LastEditTime: 2025-07-21 17:58:42
 * @LastEditors: shaojun
 * @Description: 团队实体 - MySQL 版本 (优化版)
 */
import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm'
import { RoleEnum } from '../enums'
import { BaseEntity } from './common'

@Entity('teams')
@Index(['name']) // 团队名称索引，用于搜索
@Index(['creatorId']) // 创建者索引，用于查询用户创建的团队
@Index(['isDefault', 'creatorId']) // 复合索引，快速查找用户的默认团队
@Index(['createdAt']) // 时间索引，用于排序和范围查询
@Index(['memberCount']) // 成员数量索引，用于统计和排序
export class Team extends BaseEntity {
  constructor(name?: string, icon?: string, isDefault?: boolean) {
    super()
    if (name) {
      this.name = name
    }
    if (icon) {
      this.icon = icon
    }
    if (isDefault !== undefined) {
      this.isDefault = isDefault
    } else {
      this.isDefault = false
    }
    // 初始化默认值
    this.memberCount = 0
    this.description = undefined
    this.appCount = 0
  }

  @Column({
    type: 'varchar',
    length: 100,
    comment: '团队名称',
  })
  name!: string

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否为默认团队',
  })
  isDefault: boolean

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: '团队图标URL',
  })
  icon?: string

  @Column({
    type: 'int',
    nullable: true,
    comment: '创建者用户ID',
  })
  creatorId?: number

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '创建者名称 (冗余字段)',
  })
  creatorName?: string

  @Column({
    type: 'text',
    nullable: true,
    comment: '团队描述',
  })
  description?: string

  @Column({
    type: 'int',
    default: 0,
    comment: '团队成员数量',
  })
  memberCount: number

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '团队状态 (active/inactive/archived)',
  })
  status?: string

  @Column({
    type: 'json',
    nullable: true,
    comment: '团队设置 (JSON对象)',
  })
  settings?: string

  @Column({
    type: 'int',
    default: 0,
    comment: '团队拥有的应用数量',
  })
  appCount: number

  // 优化关系映射：团队创建者
  @ManyToOne('User', { lazy: true })
  @JoinColumn({ name: 'creatorId' })
  creator!: Promise<any> // User类型

  // 优化关系映射：团队成员关系 - 使用懒加载
  @OneToMany(() => UserTeam, (userTeam) => userTeam.team, {
    lazy: true,
    cascade: true,
  })
  userTeams!: Promise<UserTeam[]>

  // 优化关系映射：团队成员详情 - 使用懒加载
  @OneToMany(() => TeamMember, (member) => member.team, {
    lazy: true,
    cascade: true,
  })
  members!: Promise<TeamMember[]>

  // 优化关系映射：团队拥有的应用
  @OneToMany('App', 'team', { lazy: true })
  apps!: Promise<any[]> // App[]类型

  // 便捷方法：检查是否为团队成员
  async isMember(userId: number): Promise<boolean> {
    const userTeams = await this.userTeams
    return userTeams.some((ut) => ut.userId === userId)
  }

  // 便捷方法：获取用户在团队中的角色
  async getUserRole(userId: number): Promise<RoleEnum | null> {
    const userTeams = await this.userTeams
    const userTeam = userTeams.find((ut) => ut.userId === userId)
    return userTeam ? userTeam.role : null
  }

  // 便捷方法：检查用户权限
  async hasPermission(userId: number, requiredRole: RoleEnum): Promise<boolean> {
    const userRole = await this.getUserRole(userId)
    if (!userRole) {
      return false
    }

    const roleHierarchy = {
      [RoleEnum.owner]: 3,
      [RoleEnum.manager]: 2,
      [RoleEnum.guest]: 1,
    }

    return roleHierarchy[userRole] >= roleHierarchy[requiredRole]
  }

  // 便捷方法：检查是否为团队创建者
  isCreatedBy(userId: number): boolean {
    return this.creatorId === userId
  }

  // 便捷方法：获取团队设置
  getSettings(): Record<string, any> {
    try {
      return this.settings ? JSON.parse(this.settings) : {}
    } catch {
      return {}
    }
  }

  // 便捷方法：设置团队配置
  setSettings(settings: Record<string, any>): void {
    this.settings = JSON.stringify(settings)
  }

  // 便捷方法：更新成员数量
  updateMemberCount(count: number): void {
    this.memberCount = Math.max(0, count)
  }

  // 便捷方法：增加成员数量
  incrementMemberCount(): void {
    this.memberCount += 1
  }

  // 便捷方法：减少成员数量
  decrementMemberCount(): void {
    this.memberCount = Math.max(0, this.memberCount - 1)
  }

  // 便捷方法：检查团队是否活跃
  isActive(): boolean {
    return this.status !== 'inactive' && this.status !== 'archived'
  }

  // 便捷方法：获取团队显示名称
  getDisplayName(): string {
    return this.name || `团队-${this.id}`
  }
}

/**
 * 团队成员实体 (优化版)
 */
@Entity('team_members')
@Index(['teamId', 'userId'], { unique: true }) // 复合唯一索引，防止重复关系
@Index(['role']) // 角色索引，用于权限查询
@Index(['userId']) // 用户索引，查询用户的团队
@Index(['joinedAt']) // 加入时间索引，用于排序
@Index(['teamId', 'role']) // 复合索引，查询团队的特定角色成员
export class TeamMember extends BaseEntity {
  constructor(teamId?: number, userId?: number, role?: RoleEnum) {
    super()
    if (teamId) {
      this.teamId = teamId
    }
    if (userId) {
      this.userId = userId
    }
    if (role) {
      this.role = role
    } else {
      this.role = RoleEnum.guest // 默认角色
    }
    // 初始化默认值
    this.joinedAt = new Date()
    this.status = 'active'
  }

  @Column({
    type: 'int',
    comment: '团队ID',
  })
  teamId!: number

  @Column({
    type: 'int',
    comment: '用户ID',
  })
  userId!: number

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '用户名 (冗余字段，便于查询)',
  })
  username?: string

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '用户邮箱 (冗余字段，便于查询)',
  })
  email?: string

  @Column({
    type: 'enum',
    enum: RoleEnum,
    default: RoleEnum.guest,
    comment: '用户在团队中的角色',
  })
  role: RoleEnum

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: '用户头像 (冗余字段，便于查询)',
  })
  userAvatar?: string

  @Column({
    type: 'datetime',
    default: () => 'CURRENT_TIMESTAMP',
    comment: '加入团队时间',
  })
  joinedAt: Date

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '邀请者名称',
  })
  invitedBy?: string

  @Column({
    type: 'int',
    nullable: true,
    comment: '邀请者用户ID',
  })
  invitedById?: number

  @Column({
    type: 'varchar',
    length: 50,
    default: 'active',
    comment: '成员状态 (active/inactive/pending)',
  })
  status: string

  @Column({
    type: 'text',
    nullable: true,
    comment: '成员备注',
  })
  note?: string

  // 优化关系映射
  @ManyToOne(() => Team, (team) => team.members)
  @JoinColumn({ name: 'teamId' })
  team!: Team

  @ManyToOne('User', { lazy: true })
  @JoinColumn({ name: 'userId' })
  user!: Promise<any> // User类型

  @ManyToOne('User', { lazy: true })
  @JoinColumn({ name: 'invitedById' })
  inviter!: Promise<any> // User类型

  // 便捷方法：检查是否为管理员角色
  isAdmin(): boolean {
    return this.role === RoleEnum.owner || this.role === RoleEnum.manager
  }

  // 便捷方法：检查是否为所有者
  isOwner(): boolean {
    return this.role === RoleEnum.owner
  }

  // 便捷方法：获取角色权重
  getRoleWeight(): number {
    const weights = {
      [RoleEnum.owner]: 100,
      [RoleEnum.manager]: 10,
      [RoleEnum.guest]: 1,
    }
    return weights[this.role] || 0
  }

  // 便捷方法：检查成员是否活跃
  isActive(): boolean {
    return this.status === 'active'
  }

  // 便捷方法：检查成员是否待审核
  isPending(): boolean {
    return this.status === 'pending'
  }

  // 便捷方法：激活成员
  activate(): void {
    this.status = 'active'
  }

  // 便捷方法：停用成员
  deactivate(): void {
    this.status = 'inactive'
  }

  // 便捷方法：获取加入天数
  getDaysJoined(): number {
    const now = new Date()
    const joinDate = new Date(this.joinedAt)
    const diffTime = Math.abs(now.getTime() - joinDate.getTime())
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }
}

// Schema 定义 =======================================

/**
 * 团队成员 Schema (优化版)
 */
export const TeamMemberSchema = z.object({
  id: z.number().openapi({ description: '成员关系ID' }),
  teamId: z.number().openapi({ description: '团队ID' }),
  userId: z.number().openapi({ description: '用户ID' }),
  username: z.string().optional().openapi({ description: '用户名' }),
  email: z.string().optional().openapi({ description: '用户邮箱' }),
  role: z.nativeEnum(RoleEnum).openapi({
    description: '团队角色',
    example: RoleEnum.guest,
  }),
  userAvatar: z.string().optional().openapi({ description: '用户头像' }),
  joinedAt: z.date().openapi({ description: '加入时间' }),
  invitedBy: z.string().optional().openapi({ description: '邀请者' }),
  invitedById: z.number().optional().openapi({ description: '邀请者ID' }),
  status: z.string().openapi({ description: '成员状态' }),
  note: z.string().optional().openapi({ description: '成员备注' }),
  createdAt: z.date().openapi({ description: '创建时间' }),
  updatedAt: z.date().openapi({ description: '更新时间' }),
})

/**
 * 团队信息 Schema (优化版)
 */
export const TeamInfo = z.object({
  id: z.number().openapi({ description: '团队ID' }),
  name: z.string().openapi({
    description: '团队名称',
    example: '开发团队',
  }),
  icon: z.string().optional().openapi({
    description: '团队图标URL',
    example: 'https://example.com/team-icon.png',
  }),
  isDefault: z.boolean().openapi({
    description: '是否为默认团队',
    example: false,
  }),
  creatorId: z.number().optional().openapi({ description: '创建者ID' }),
  creatorName: z.string().optional().openapi({ description: '创建者名称' }),
  description: z.string().optional().openapi({ description: '团队描述' }),
  memberCount: z.number().openapi({
    description: '成员数量',
    example: 5,
  }),
  status: z.string().optional().openapi({ description: '团队状态' }),
  settings: z.record(z.string(), z.unknown()).optional().openapi({ description: '团队设置' }),
  appCount: z.number().openapi({ description: '应用数量' }),
  members: z.array(TeamMemberSchema).optional().openapi({
    description: '团队成员列表',
  }),
  createdAt: z.date().openapi({ description: '创建时间' }),
  updatedAt: z.date().openapi({ description: '更新时间' }),
}).openapi('TeamInfo')

/**
 * 创建团队 Schema (优化版)
 */
export const CreateTeamSchema = z.object({
  name: z.string().min(1).max(100).openapi({
    description: '团队名称，1-100字符',
    example: '开发团队',
  }),
  icon: z.string().url().optional().openapi({
    description: '团队图标URL',
    example: 'https://example.com/team-icon.png',
  }),
  description: z.string().max(500).optional().openapi({
    description: '团队描述，最多500字符',
    example: '负责产品开发的核心团队',
  }),
  isDefault: z.boolean().optional().openapi({
    description: '是否设为默认团队',
    example: false,
  }),
  settings: z.record(z.string(), z.unknown()).optional().openapi({ description: '团队设置' }),
})

/**
 * 更新团队 Schema (优化版)
 */
export const UpdateTeamSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  icon: z.string().url().optional(),
  description: z.string().max(500).optional(),
  status: z.enum(['active', 'inactive', 'archived']).optional(),
  settings: z.record(z.string(), z.unknown()).optional(),
})

/**
 * 邀请成员 Schema (优化版)
 */
export const InviteMemberSchema = z.object({
  email: z.string().email().openapi({
    description: '邀请用户的邮箱',
    example: '<EMAIL>',
  }),
  role: z.nativeEnum(RoleEnum).optional().openapi({
    description: '分配的角色，默认为guest',
    example: RoleEnum.guest,
  }),
  note: z.string().max(200).optional().openapi({
    description: '邀请备注',
    example: '前端开发工程师',
  }),
})

/**
 * 批量邀请成员 Schema (优化版)
 */
export const BatchInviteMemberSchema = z.object({
  emails: z.array(z.string().email()).min(1).max(10).openapi({
    description: '邀请用户的邮箱列表，最多10个',
    example: ['<EMAIL>', '<EMAIL>'],
  }),
  role: z.nativeEnum(RoleEnum).optional().openapi({
    description: '分配的角色，默认为guest',
    example: RoleEnum.guest,
  }),
  note: z.string().max(200).optional().openapi({
    description: '邀请备注',
  }),
})

/**
 * 更新成员角色 Schema (优化版)
 */
export const UpdateMemberRoleSchema = z.object({
  userId: z.number().openapi({ description: '用户ID' }),
  role: z.nativeEnum(RoleEnum).openapi({
    description: '新角色',
    example: RoleEnum.manager,
  }),
  note: z.string().max(200).optional().openapi({
    description: '角色变更备注',
  }),
})

/**
 * 更新成员状态 Schema
 */
export const UpdateMemberStatusSchema = z.object({
  userId: z.number().openapi({ description: '用户ID' }),
  status: z.enum(['active', 'inactive', 'pending']).openapi({
    description: '成员状态',
    example: 'active',
  }),
  note: z.string().max(200).optional().openapi({
    description: '状态变更备注',
  }),
})

// 导出关系实体和类型
import type { App } from './app'
import type { User } from './user'
import { UserTeam } from './user'
