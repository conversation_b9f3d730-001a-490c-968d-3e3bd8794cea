import { z } from '@cfe-node/koa-swagger-decorator'
/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2024-04-22 11:41:13
 * @LastEditTime: 2025-07-11 13:12:05
 * @LastEditors: shaojun
 * @Description: 构建实体 - MySQL 版本
 */
import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm'
import { BaseEntity } from './common'

@Entity('builds')
@Index(['appId'])
@Index(['bundleId'])
@Index(['platform'])
export class Build extends BaseEntity {
  constructor(platform?: string, bundleId?: string, buildNo?: number, appId?: number) {
    super()
    // 初始化必需属性的默认值
    this.platform = platform || ''
    this.bundleId = bundleId || ''
    this.buildNo = buildNo || 0
    this.appId = appId || 0
    this.uploadAt = new Date()
  }

  @Column({
    type: 'int',
    comment: '应用ID',
  })
  appId: number

  @Column({
    type: 'varchar',
    length: 20,
    comment: '平台类型 (iOS/Android)',
  })
  platform: string

  @Column({
    type: 'varchar',
    length: 255,
    comment: '包标识符',
  })
  bundleId: string

  @Column({
    type: 'bigint',
    default: 0,
    comment: '构建编号',
  })
  buildNo: number

  @Column({
    type: 'datetime',
    comment: '上传时间',
  })
  uploadAt: Date

  // 关系映射：所属应用
  @ManyToOne('App', 'builds')
  @JoinColumn({ name: 'appId' })
  app: any // App类型

  // 便捷方法：获取构建标识符
  getBuildIdentifier(): string {
    return `${this.platform}-${this.bundleId}-${this.buildNo}`
  }
}

/**
 * 构建信息 Schema
 */
export const BuildInfo = z.object({
  id: z.number().openapi({ description: '构建ID' }),
  appId: z.number().openapi({ description: '应用ID' }),
  platform: z.string().openapi({
    description: '平台类型',
    example: 'iOS',
  }),
  bundleId: z.string().openapi({
    description: '包标识符',
    example: 'com.example.app',
  }),
  buildNo: z.number().openapi({
    description: '构建编号',
    example: 1001,
  }),
  uploadAt: z.date().openapi({ description: '上传时间' }),
  createdAt: z.date().openapi({ description: '创建时间' }),
  updatedAt: z.date().openapi({ description: '更新时间' }),
}).openapi('BuildInfo')

/**
 * 创建构建 Schema
 */
export const CreateBuildSchema = z.object({
  appId: z.number().openapi({ description: '应用ID' }),
  platform: z.string().openapi({ description: '平台类型' }),
  bundleId: z.string().openapi({ description: '包标识符' }),
  buildNo: z.number().openapi({ description: '构建编号' }),
})
