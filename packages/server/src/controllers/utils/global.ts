import type { ParameterObject, ReferenceObject, SchemaObject } from 'openapi3-ts/oas30'

/*
 * @Author: shao<PERSON>
 * @Date: 2024-04-09 11:34:14
 * @LastEditTime: 2025-07-21 08:58:55
 * @LastEditors: shaojun
 * @Description: 全局公用的类型定义
 */
import type { ZodTypeAny } from 'zod'
import { refLink, z } from '@cfe-node/koa-swagger-decorator'
import { globals } from '@/typeorm/entity'

// 定义全局的schema>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

export const JWT_AUTH = (key = 'Authorization'): ParameterObject => {
  return {
    in: 'header',
    name: key,
    required: true,
    schema: {
      type: 'string',
    },
  }
}
export const AttachInfo: ParameterObject = {
  in: 'header',
  name: 'attach-info',
  required: true,
  schema: {
    type: 'string',
  },
}

export const StringData = z.string().openapi({ example: '' })

export const R_StringData_ = z.object({
  resultCode: z.number().openapi({ example: 200 }),
  resultMessage: z.string().openapi({ example: 'success' }),
  data: z.string().openapi({ example: '' }),
})
// 分页
export const PageReq = z.object({
  page: z.object({
    pageIndex: z.number().openapi({ example: 1 }),
    pageSize: z.number().openapi({ example: 10 }),
    sortItems: z.array(
      z.object({
        sortBy: z.string(),
        direction: z.string(),
      }),
    ).optional(),
  }),
})
//
export const PagesRes = z.object({
  pageIndex: z.number().openapi({ example: 1 }),
  pageSize: z.number().openapi({ example: 10 }),
  total: z.number().optional().openapi({ example: 100 }),
  pages: z.number().optional().openapi({ example: 100 }),
  sortItems: z.array(
    z.object({
      sortBy: z.string(),
      direction: z.string(),
    }),
  ).optional(),
})

export type IRStringData = z.infer<typeof R_StringData_>

// 枚举所有全局schema的key
export enum GlobalSchemasKeyEnum {
  R_StringData_ = 'R_StringData_',
  StringData = 'StringData',
  UserInfo = 'UserInfo',
  List_UserInfo_ = 'List_UserInfo_', // List<UserInfo> 不需要定义globalSchemas
  TeamInfo = 'TeamInfo',
  List_TeamInfo_ = 'List_TeamInfo_', // List<TeamInfo> 不需要定义globalSchemas
  AppInfo = 'AppInfo',
  List_AppInfo_ = 'List_AppInfo_', // List<AppInfo> 不需要定义globalSchemas
  VersionInfo = 'VersionInfo',
  List_VersionInfo_ = 'List_VersionInfo_', // List<VersionInfo> 不需要定义globalSchemas
  BuildInfo = 'BuildInfo',
  List_BuildInfo_ = 'List_BuildInfo_', // List<BuildInfo> 不需要定义globalSchemas
  MiniappInfo = 'MiniappInfo',
  List_MiniappInfo_ = 'List_MiniappInfo_', // List<MiniappInfo> 不需要定义globalSchemas
  InviteInfo = 'InviteInfo',
  List_InviteInfo_ = 'List_InviteInfo_', // List<InviteInfo> 不需要定义globalSchemas
  MessageInfo = 'MessageInfo',
  List_MessageInfo_ = 'List_MessageInfo_', // List<MessageInfo> 不需要定义globalSchemas
  DownloadInfo = 'DownloadInfo',
  List_DownloadInfo_ = 'List_DownloadInfo_', // List<DownloadInfo> 不需要定义globalSchemas
}
// globalSchemas中所有的可以必须在GlobalSchemasKeyEnum中定义
// 注意：直接引用原始 schemas，不使用展开操作符，以保持对象引用一致性
export const globalSchemas: Partial<Record<GlobalSchemasKeyEnum, ZodTypeAny>> = {
  R_StringData_,
  StringData,
  ...globals,
  // AppInfo: globals.AppInfo,
  // BuildInfo: globals.BuildInfo,
  // DownloadInfo: globals.DownloadInfo,
  // InviteInfo: globals.InviteInfo,
  // MessageInfo: globals.MessageInfo,
  // MiniappInfo: globals.MiniappInfo,
  // TeamInfo: globals.TeamInfo,
  // UserInfo: globals.UserInfo,
  // VersionInfo: globals.VersionInfo,
}
// 引用全局schema
export const globalReferenceObject = (globalKey: GlobalSchemasKeyEnum): SchemaObject | ReferenceObject => {
  // match /List_(.*)_/g
  const match = /List_(.*)_/.exec(globalKey)
  if (match) {
    // list ReferenceObject
    const theKey = match[1]
    return {
      type: 'array',
      items: {
        $ref: refLink({ refId: theKey }),
      },
    }
  } else {
    // object ReferenceObject
    return {
      $ref: refLink({ refId: globalKey }),
    }
  }
}
export type GlobalSchemasKeysType = keyof typeof globalSchemas
export type GlobalSchemasValueType<K extends keyof typeof globalSchemas> = typeof globalSchemas[K]
