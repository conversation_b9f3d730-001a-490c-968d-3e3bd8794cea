import type { Context } from 'koa'
import type { ControllerConfig } from '../../../koa-swagger-decorator/src'
import { Buffer } from 'node:buffer'
import process from 'node:process'
import { Body, Controller, Header, Middlewares, Responses, RouteConfig, z } from '../../../koa-swagger-decorator/src'
import { AppException } from '../middlewares'
import { GlobalSchemasKeyEnum } from './utils/global'

// 定义请求体 Schema
export const UserLoginSchema = z.object({
  username: z.string().min(3).max(20).openapi({
    description: '用户名',
    example: 'admin',
  }),
  password: z.string().min(6).max(20).openapi({
    description: '密码',
    example: '123456',
  }),
})

// 定义响应 Schema
const UserInfoSchema = z.object({
  id: z.number().openapi({ example: 1 }),
  username: z.string().openapi({ example: 'admin' }),
  nickname: z.string().optional().openapi({ example: '管理员' }),
  avatar: z.string().optional().openapi({ example: 'https://example.com/avatar.png' }),
  role: z.enum(['admin', 'user']).openapi({ example: 'admin' }),
})

// 定义登录成功数据 Schema（只包含数据部分，不包含 resultCode 和 resultMessage）
const LoginSuccessDataSchema = z.object({
  user: UserInfoSchema,
  token: z.string().openapi({
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    description: 'JWT 访问令牌',
  }),
})

// 定义错误响应 Schema
const ErrorResponseSchema = z.object({
  resultCode: z.number().openapi({ example: 401 }),
  resultMessage: z.string().openapi({ example: '用户名或密码错误' }),
})

// 简单的日志中间件示例
const loggerMiddleware = async (ctx: Context, next: () => Promise<void>) => {
  const start = Date.now()
  console.log(`[${new Date().toISOString()}] ${ctx.method} ${ctx.path} - Start`)
  await next()
  const ms = Date.now() - start
  console.log(`[${new Date().toISOString()}] ${ctx.method} ${ctx.path} - ${ms}ms`)
}

// 简单的限流中间件示例
const rateLimitMiddleware = async (ctx: Context, next: () => Promise<void>) => {
  // 这里可以实现实际的限流逻辑
  // 例如：检查 IP 地址的请求频率
  console.log(`Rate limit check for IP: ${ctx.ip}`)
  await next()
}

// 控制器配置
const controllerConfig: ControllerConfig = {
  tags: ['用户管理'],
  paths: {
    parameters: [
      {
        name: 'Authorization',
        in: 'header', // 使用类型断言确保 'in' 的类型为 ParameterLocation
        required: false, // 登录接口不需要认证
        schema: {
          type: 'string',
        },
        description: 'Bearer token，登录后获取',
      },
    ],
  },
}

@Controller(controllerConfig)
export class UserController {
  // @request('post', '/user/login')
  // @summary('登录')
  // @header(Headers.getHeaderSchema({ needAuthorization: false }))
  // @body(loginBody)
  // @responses({
  //   200: { description: 'success', schema: GlobalSchemas.default(GlobalSchemasKey.User) },
  // })
  // static async login(ctx: Context) {
  //   const requestBody: LoginRequest = ctx.request.body as LoginRequest
  //   console.log(requestBody)
  //   const userRepository: MongoRepository<User> = getMongoRepository(User)
  //   const teamRepository: MongoRepository<Team> = getMongoRepository(Team)

  //   // 判断是否开放 ldap，如果开放ldap,
  //   // 根据ldap的用户信息生成新用户
  //   if (config.ldap.openLdap) {
  //     const ldapUser = await Ldap.auth(requestBody.username, requestBody.password)

  //     const user: User | undefined = await userRepository.findOne({
  //       username: requestBody.username,
  //     })

  //     if (ldapUser && (!user || user.username !== ldapUser.name)) {
  //       console.log(`user${ldapUser}`)
  //       const password = await bcrypt.hash(requestBody.password, 10)

  //       const { username, email } = ldapUser
  //       const newUser = await userRepository.save(new User(username, password, email))
  //       const team = await teamRepository.save(new Team('我的团队', ''))
  //       team.creatorId = newUser.id
  //       team.members = [
  //         new Member(String(newUser.id), newUser.username, newUser.email, RoleEnum.owner),
  //       ]
  //       newUser.teams = [new UserTeam(String(team.id), team.name, '', RoleEnum.owner)]
  //       // todo:是否需要事物处理
  //       userRepository.update(newUser.id, newUser)
  //       teamRepository.update(team.id!, team)
  //     }
  //   }
  //   const user: User | undefined = await userRepository.findOne({ username: requestBody.username })
  //   if (user) {
  //     if (user.password) {
  //       const valide = await bcrypt.compare(requestBody.password, user.password)
  //       if (!valide) throw new Error('用户名或密码错误')
  //     } else {
  //       throw new Error('用户名或密码错误')
  //     }
  //   } else {
  //     throw new Error('用户不存在')
  //   }

  //   user.token = jwt.sign(
  //     {
  //       data: {
  //         id: user.id,
  //         username: user.username,
  //         email: user.email,
  //       },
  //       exp: Math.floor(Date.now() / 1000) + 60 * 60,
  //     },
  //     config.secret,
  //   )
  //   ctx.body = responseWrapper3<User>({ data: user })
  // }

  @RouteConfig({
    method: 'post',
    path: '/user/login',
    summary: '用户登录',
    description: '用户登录接口，返回用户信息和 JWT token',
  })
  @Body(UserLoginSchema)
  // 使用优化后的多状态码响应支持 - 新功能
  @Responses({
    200: {
      description: '登录成功',
      schema: GlobalSchemasKeyEnum.UserInfo,
    },
    400: {
      description: '请求参数错误',
      schema: GlobalSchemasKeyEnum.StringData,
    },
    401: {
      description: '用户名或密码错误',
      schema: GlobalSchemasKeyEnum.StringData,
    },
    429: {
      description: '请求过于频繁',
      schema: GlobalSchemasKeyEnum.StringData,
    },
    500: {
      description: '服务器内部错误',
      schema: GlobalSchemasKeyEnum.StringData,
    },
  })
  // 使用完整的请求头支持 - 新功能
  @Header({
    'Content-Type': {
      required: true,
      schema: { type: 'string', enum: ['application/json'] },
      description: '请求内容类型，必须为 application/json',
    },
    'User-Agent': {
      required: false,
      schema: { type: 'string' },
      description: '用户代理信息',
    },
    'X-Request-ID': {
      required: false,
      schema: { type: 'string' },
      description: '请求追踪ID',
    },
  })
  // 使用中间件装饰器 - 新功能
  @Middlewares([loggerMiddleware, rateLimitMiddleware])
  async login(ctx: Context) {
    try {
      const { username, password } = ctx.request.body

      // 基本的输入验证
      if (!username || !password) {
        throw new AppException(400, '用户名和密码不能为空')
      }

      // 这里是示例，实际应该查询数据库并验证密码哈希
      if (username === 'admin' && password === '123456') {
        // 生成 JWT token (这里使用模拟 token)
        const token = `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.${Buffer.from(JSON.stringify({
          id: 1,
          username: 'admin',
          role: 'admin',
          exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60), // 7天过期
        })).toString('base64')}.mock-signature`

        const user = {
          id: 1,
          username: 'admin',
          nickname: '管理员',
          avatar: 'https://example.com/avatar.png',
          role: 'admin' as const,
        }

        ctx.status = 200
        ctx.body = {
          data: {
            code: 0,
            data: {
              user,
              token,
            },
            message: '登录成功',
          },
          resultCode: 200,
          resultMessage: 'success',
        }
      } else {
        throw new AppException(401, '用户名或密码错误')
      }
    } catch (error) {
      console.error('Login error:', error)
      // 如果是 AppException，直接重新抛出
      if (error instanceof AppException) {
        throw error
      }
      // 其他错误包装为 AppException
      throw new AppException(500, '服务器内部错误')
    }
  }

  @RouteConfig({
    method: 'get',
    path: '/user/info',
    summary: '获取用户信息',
    description: '获取当前登录用户的详细信息',
  })
  // 使用优化后的多状态码响应支持
  @Responses({
    200: {
      description: '获取成功',
      schema: z.object({
        code: z.number().openapi({ example: 0 }),
        data: UserInfoSchema,
        message: z.string().openapi({ example: '获取成功' }),
      }).openapi('GetUserInfoData'),
    },
    401: {
      description: '未授权，token 无效或已过期',
      schema: GlobalSchemasKeyEnum.StringData,
    },
    403: {
      description: '权限不足',
      schema: GlobalSchemasKeyEnum.StringData,
    },
    500: {
      description: '服务器内部错误',
      schema: GlobalSchemasKeyEnum.StringData,
    },
  })
  // 使用完整的请求头支持
  @Header({
    'Authorization': {
      required: true,
      schema: { type: 'string', pattern: '^Bearer .+' },
      description: 'Bearer token，格式: Bearer <token>',
    },
    'X-Request-ID': {
      required: false,
      schema: { type: 'string' },
      description: '请求追踪ID',
    },
  })
  // 使用中间件装饰器
  @Middlewares([loggerMiddleware])
  async getUserInfo(ctx: Context) {
    try {
      // 简单的 token 验证示例
      const authHeader = ctx.headers.authorization
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        throw new AppException(401, '缺少有效的 Authorization 头')
      }

      const token = authHeader.substring(7) // 移除 "Bearer " 前缀

      // 这里应该验证 JWT token，现在只是简单检查
      if (!token || token === 'invalid') {
        throw new AppException(401, 'Token 无效或已过期')
      }

      // 这里应该从 token 中解析用户信息，现在返回示例数据
      const userInfo = {
        id: 1,
        username: 'admin',
        nickname: '管理员',
        avatar: 'https://example.com/avatar.png',
        role: 'admin' as const,
      }

      ctx.status = 200
      ctx.body = {
        data: {
          code: 0,
          data: userInfo,
          message: '获取成功',
        },
        resultCode: 200,
        resultMessage: 'success',
      }
    } catch (error) {
      console.error('Get user info error:', error)
      // 如果是 AppException，直接重新抛出
      if (error instanceof AppException) {
        throw error
      }
      // 其他错误包装为 AppException
      throw new AppException(500, '服务器内部错误')
    }
  }
}
