{"openapi": "3.0.0", "info": {"title": "CFE Node Server API", "version": "1.0.0", "description": "基于 Node.js + Koa + TypeScript 的后端服务框架"}, "paths": {"/api/user/login": {"post": {"summary": "用户登录", "description": "用户登录接口，返回用户信息和 JWT token", "operationId": "login", "tags": ["用户管理"], "parameters": [{"name": "Authorization", "in": "header", "required": false, "schema": {"type": "string"}, "description": "Bearer token，登录后获取"}, {"required": true, "schema": {"type": "string", "enum": ["application/json"]}, "description": "请求内容类型，必须为 application/json", "name": "Content-Type", "in": "header"}, {"required": false, "schema": {"type": "string"}, "description": "用户代理信息", "name": "User-Agent", "in": "header"}, {"required": false, "schema": {"type": "string"}, "description": "请求追踪ID", "name": "X-Request-ID", "in": "header"}], "methodName": "login", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "登录成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_LoginData_"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_StringData_"}}}}, "401": {"description": "用户名或密码错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_StringData_"}}}}, "429": {"description": "请求过于频繁", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_StringData_"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_StringData_"}}}}}}}, "/api/user/info": {"get": {"summary": "获取用户信息", "description": "获取当前登录用户的详细信息", "operationId": "getUserInfo", "tags": ["用户管理"], "parameters": [{"name": "Authorization", "in": "header", "required": false, "schema": {"type": "string"}, "description": "Bearer token，登录后获取"}, {"required": true, "schema": {"type": "string", "pattern": "^Bearer .+"}, "description": "Bearer token，格式: Bearer <token>", "name": "Authorization", "in": "header"}, {"required": false, "schema": {"type": "string"}, "description": "请求追踪ID", "name": "X-Request-ID", "in": "header"}], "methodName": "getUserInfo", "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_GetUserInfoData_"}}}}, "401": {"description": "未授权，token 无效或已过期", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_StringData_"}}}}, "403": {"description": "权限不足", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_StringData_"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_StringData_"}}}}}}}, "/api/health": {"get": {"summary": "健康检查", "description": "服务健康检查接口", "operationId": "getHealth", "tags": ["系统健康检查"], "parameters": [], "methodName": "getHealth", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_GetHealthData_"}}}}}}}}, "components": {"parameters": {"0": {"name": "Authorization", "in": "header", "required": false, "schema": {"type": "string"}, "description": "Bearer token，登录后获取"}}, "schemas": {"R_LoginData_": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/LoginData"}, "resultCode": {"type": "number", "example": 200}, "resultMessage": {"type": "string", "example": "success"}}, "required": ["resultCode", "resultMessage", "data"]}, "R_GetUserInfoData_": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/GetUserInfoData"}, "resultCode": {"type": "number", "example": 200}, "resultMessage": {"type": "string", "example": "success"}}, "required": ["resultCode", "resultMessage", "data"]}, "R_GetHealthData_": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/GetHealthData"}, "resultCode": {"type": "number", "example": 200}, "resultMessage": {"type": "string", "example": "success"}}, "required": ["resultCode", "resultMessage", "data"]}, "R_StringData_": {"type": "object", "properties": {"resultCode": {"type": "number"}, "resultMessage": {"type": "string"}, "data": {"type": "string"}}, "required": ["resultCode", "resultMessage", "data"]}, "StringData": {"type": "string"}, "AppInfo": {"type": "object", "properties": {"id": {"type": "number"}, "platform": {"type": "string"}, "bundleId": {"type": "string"}, "bundleName": {"type": "string"}, "appName": {"type": "string"}, "currentVersion": {"type": "string"}, "creatorId": {"type": "number"}, "creator": {"type": "string"}, "icon": {"type": "string"}, "describe": {"type": "string"}, "shortUrl": {"type": "string"}, "autoPublish": {"type": "boolean"}, "installWithPwd": {"type": "boolean"}, "installPwd": {"type": "string"}, "appLevel": {"type": "string"}, "ownerId": {"type": "number"}, "changelog": {"type": "string"}, "updateMode": {"type": "string", "enum": ["silent", "normal", "force"]}, "releaseVersionCode": {"type": "string"}, "releaseVersionId": {"type": "number"}, "grayReleaseVersionId": {"type": "number"}, "totalDownloadCount": {"type": "number"}, "dailyStats": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "appId": {"type": "number"}, "statDate": {"type": "string", "format": "date-time"}, "downloadCount": {"type": "number"}, "viewCount": {"type": "number"}, "createdAt": {"type": "string", "format": "date-time"}}, "required": ["id", "appId", "statDate", "downloadCount", "viewCount", "createdAt"]}}, "grayStrategies": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "appId": {"type": "number"}, "name": {"type": "string"}, "description": {"type": "string"}, "ipType": {"type": "string", "enum": ["white", "black"]}, "count": {"type": "number"}, "ipList": {"type": "array", "items": {"type": "string"}}, "downloadCountLimit": {"type": "number"}, "updateMode": {"type": "string", "enum": ["silent", "normal", "force"]}, "active": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}}, "required": ["id", "appId", "ipType", "updateMode", "active", "createdAt"]}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "platform", "bundleId", "bundleName", "appName", "currentVersion", "autoPublish", "installWithPwd", "appLevel", "updateMode", "totalDownloadCount", "createdAt", "updatedAt"]}, "BuildInfo": {"type": "object", "properties": {"id": {"type": "number"}, "appId": {"type": "number"}, "platform": {"type": "string"}, "bundleId": {"type": "string"}, "buildNo": {"type": "number"}, "uploadAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "appId", "platform", "bundleId", "buildNo", "uploadAt", "createdAt", "updatedAt"]}, "DownloadInfo": {"type": "object", "properties": {"id": {"type": "number"}, "appId": {"type": "number"}, "versionId": {"type": "number"}, "userId": {"type": "number"}, "clientIp": {"type": "string"}, "userAgent": {"type": "string"}, "downloadType": {"type": "string"}, "remark": {"type": "string"}, "image": {"type": "string"}, "param": {"type": "string"}, "page": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "appId", "downloadType", "createdAt", "updatedAt"]}, "InviteInfo": {"type": "object", "properties": {"id": {"type": "number"}, "userName": {"type": "string"}, "teamId": {"type": "number"}, "type": {"type": "string", "enum": ["TEAM"]}, "status": {"type": "string", "enum": ["EBL", "DBL"]}, "emails": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "inviteId": {"type": "number"}, "email": {"type": "string", "format": "email"}, "status": {"type": "string", "enum": ["EBL", "DBL"]}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "inviteId", "email", "status", "createdAt", "updatedAt"]}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "userName", "teamId", "type", "status", "createdAt", "updatedAt"]}, "MessageInfo": {"type": "object", "properties": {"id": {"type": "number"}, "category": {"type": "string"}, "content": {"type": "string"}, "senderId": {"type": "number"}, "receiverId": {"type": "number"}, "sendAt": {"type": "string", "format": "date-time"}, "status": {"type": "string"}, "data": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "receiverId", "sendAt", "status", "createdAt", "updatedAt"]}, "MiniappInfo": {"type": "object", "properties": {"id": {"type": "number"}, "appName": {"type": "string"}, "appId": {"type": "string"}, "platform": {"type": "string"}, "pagePath": {"type": "string"}, "appEnv": {"type": "boolean"}, "ownerId": {"type": "number"}, "creatorId": {"type": "number"}, "creator": {"type": "string"}, "appSecret": {"type": "string"}, "icon": {"type": "string"}, "describe": {"type": "string"}, "changelog": {"type": "string"}, "downloadCodeImages": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "miniappId": {"type": "number"}, "type": {"type": "string"}, "image": {"type": "string"}, "env": {"type": "string"}, "developer": {"type": "string"}, "version": {"type": "string"}, "desc": {"type": "string"}, "pagePath": {"type": "string"}, "searchQuery": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "miniappId", "type", "image", "env", "createdAt", "updatedAt"]}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "appName", "appId", "platform", "pagePath", "appEnv", "createdAt", "updatedAt"]}, "TeamInfo": {"type": "object", "properties": {"id": {"type": "number"}, "name": {"type": "string"}, "icon": {"type": "string"}, "isDefault": {"type": "boolean"}, "creatorId": {"type": "number"}, "creatorName": {"type": "string"}, "description": {"type": "string"}, "memberCount": {"type": "number"}, "status": {"type": "string"}, "settings": {"type": "object", "additionalProperties": {"type": "object", "description": "Unknown type"}}, "appCount": {"type": "number"}, "members": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "teamId": {"type": "number"}, "userId": {"type": "number"}, "username": {"type": "string"}, "email": {"type": "string"}, "role": {"type": "string", "enum": ["owner", "manager", "guest"]}, "userAvatar": {"type": "string"}, "joinedAt": {"type": "string", "format": "date-time"}, "invitedBy": {"type": "string"}, "invitedById": {"type": "number"}, "status": {"type": "string"}, "note": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "teamId", "userId", "role", "joinedAt", "status", "createdAt", "updatedAt"]}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "name", "isDefault", "memberCount", "appCount", "createdAt", "updatedAt"]}, "UserInfo": {"type": "object", "properties": {"id": {"type": "number"}, "username": {"type": "string"}, "userAvatar": {"type": "string"}, "userAvatarHistory": {"type": "array", "items": {"type": "string"}}, "email": {"type": "string", "format": "email"}, "token": {"type": "string"}, "apiToken": {"type": "string", "nullable": true}, "mobile": {"type": "string"}, "qq": {"type": "string"}, "company": {"type": "string"}, "career": {"type": "string"}, "userTeams": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "userId": {"type": "number"}, "teamId": {"type": "number"}, "teamName": {"type": "string"}, "teamIcon": {"type": "string"}, "role": {"type": "string", "enum": ["owner", "manager", "guest"]}, "createdAt": {"type": "string", "format": "date-time"}}, "required": ["id", "userId", "teamId", "role", "createdAt"]}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "username", "userAvatar", "email", "token", "apiToken", "mobile", "qq", "company", "career", "createdAt", "updatedAt"]}, "VersionInfo": {"type": "object", "properties": {"id": {"type": "number"}, "appId": {"type": "number"}, "bundleId": {"type": "string"}, "icon": {"type": "string"}, "versionStr": {"type": "string"}, "versionCode": {"type": "string"}, "uploadAt": {"type": "string", "format": "date-time"}, "uploader": {"type": "string"}, "uploaderId": {"type": "number"}, "size": {"type": "number"}, "active": {"type": "boolean"}, "downloadUrl": {"type": "string"}, "downloadCount": {"type": "number"}, "fileDownloadUrl": {"type": "string"}, "installUrl": {"type": "string"}, "showOnDownloadPage": {"type": "boolean"}, "appLevel": {"type": "string"}, "changelog": {"type": "string"}, "md5": {"type": "string"}, "hidden": {"type": "boolean"}, "updateMode": {"type": "string", "enum": ["silent", "normal", "force"]}, "released": {"type": "boolean"}, "versionTag": {"type": "string"}, "versionNote": {"type": "string"}, "sortWeight": {"type": "number"}, "status": {"type": "string"}, "minOsVersion": {"type": "string"}, "supportedDevices": {"type": "array", "items": {"type": "string"}}, "metadata": {"type": "object", "additionalProperties": {"type": "object", "description": "Unknown type"}}, "releasedAt": {"type": "string", "format": "date-time"}, "buildNumber": {"type": "number"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "appId", "uploadAt", "size", "active", "downloadCount", "showOnDownloadPage", "hidden", "updateMode", "released", "sortWeight", "status", "createdAt", "updatedAt"]}, "LoginRequest": {"type": "object", "properties": {"username": {"type": "string", "minLength": 3, "maxLength": 20, "description": "用户名", "example": "admin"}, "password": {"type": "string", "minLength": 6, "maxLength": 20, "description": "密码", "example": "123456"}}, "required": ["username", "password"]}, "LoginData": {"type": "object", "properties": {"user": {"type": "object", "properties": {"id": {"type": "number", "example": 1}, "username": {"type": "string", "example": "admin"}, "nickname": {"type": "string", "example": "管理员"}, "avatar": {"type": "string", "example": "https://example.com/avatar.png"}, "role": {"type": "string", "enum": ["admin", "user"], "example": "admin"}}, "required": ["id", "username", "role"]}, "token": {"type": "string", "description": "JWT 访问令牌", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}}, "required": ["user", "token"]}, "GetUserInfoData": {"type": "object", "properties": {"code": {"type": "number", "example": 0}, "data": {"type": "object", "properties": {"id": {"type": "number", "example": 1}, "username": {"type": "string", "example": "admin"}, "nickname": {"type": "string", "example": "管理员"}, "avatar": {"type": "string", "example": "https://example.com/avatar.png"}, "role": {"type": "string", "enum": ["admin", "user"], "example": "admin"}}, "required": ["id", "username", "role"]}, "message": {"type": "string", "example": "获取成功"}}, "required": ["code", "data", "message"]}, "GetHealthData": {"type": "object", "properties": {"status": {"type": "string", "description": "服务状态", "example": "ok"}, "timestamp": {"type": "string", "description": "时间戳", "example": "2025-07-21T02:56:03.006Z"}, "db": {"anyOf": [{"type": "string"}, {"nullable": true}, {"nullable": true}], "description": "数据库状态", "example": "ok"}}, "required": ["status", "timestamp"]}}}, "securityDefinitions": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "Authorization"}}}