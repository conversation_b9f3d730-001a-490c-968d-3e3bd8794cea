{"openapi": "3.0.0", "info": {"title": "CFE Node Server API", "version": "1.0.0", "description": "基于 Node.js + Koa + TypeScript 的后端服务框架"}, "paths": {"/api/user/login": {"post": {"summary": "用户登录", "description": "用户登录接口，返回用户信息和 JWT token", "operationId": "login", "tags": ["用户管理"], "parameters": [{"name": "Authorization", "in": "header", "required": false, "schema": {"type": "string"}, "description": "Bearer token，登录后获取"}, {"required": true, "schema": {"type": "string", "enum": ["application/json"]}, "description": "请求内容类型，必须为 application/json", "name": "Content-Type", "in": "header"}, {"required": false, "schema": {"type": "string"}, "description": "用户代理信息", "name": "User-Agent", "in": "header"}, {"required": false, "schema": {"type": "string"}, "description": "请求追踪ID", "name": "X-Request-ID", "in": "header"}], "methodName": "login", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "登录成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_LoginVO_"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_StringData_"}}}}, "401": {"description": "用户名或密码错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_StringData_"}}}}, "429": {"description": "请求过于频繁", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_StringData_"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_StringData_"}}}}}}}, "/api/user/info": {"get": {"summary": "获取用户信息", "description": "获取当前登录用户的详细信息", "operationId": "getUserInfo", "tags": ["用户管理"], "parameters": [{"name": "Authorization", "in": "header", "required": false, "schema": {"type": "string"}, "description": "Bearer token，登录后获取"}, {"required": true, "schema": {"type": "string", "pattern": "^Bearer .+"}, "description": "Bearer token，格式: Bearer <token>", "name": "Authorization", "in": "header"}, {"required": false, "schema": {"type": "string"}, "description": "请求追踪ID", "name": "X-Request-ID", "in": "header"}], "methodName": "getUserInfo", "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_GetUserInfoVO_"}}}}, "401": {"description": "未授权，token 无效或已过期", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_StringData_"}}}}, "403": {"description": "权限不足", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_StringData_"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_StringData_"}}}}}}}, "/api/health": {"get": {"summary": "健康检查", "description": "服务健康检查接口", "operationId": "getHealth", "tags": ["系统健康检查"], "parameters": [], "methodName": "getHealth", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_GetHealthVO_"}}}}}}}}, "components": {"parameters": {"0": {"name": "Authorization", "in": "header", "required": false, "schema": {"type": "string"}, "description": "Bearer token，登录后获取"}}, "schemas": {"R_LoginVO_": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/LoginVO"}, "resultCode": {"type": "number", "example": 200}, "resultMessage": {"type": "string", "example": "success"}}, "required": ["resultCode", "resultMessage", "data"]}, "R_GetUserInfoVO_": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/GetUserInfoVO"}, "resultCode": {"type": "number", "example": 200}, "resultMessage": {"type": "string", "example": "success"}}, "required": ["resultCode", "resultMessage", "data"]}, "R_GetHealthVO_": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/GetHealthVO"}, "resultCode": {"type": "number", "example": 200}, "resultMessage": {"type": "string", "example": "success"}}, "required": ["resultCode", "resultMessage", "data"]}, "R_StringData_": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "object", "shape": {"resultCode": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "resultMessage": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "data": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}}, "StringData": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "AppInfo": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "object", "shape": {"id": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "platform": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "bundleId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "bundleName": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "appName": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "currentVersion": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "creatorId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}}}, "creator": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "icon": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "describe": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "shortUrl": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "autoPublish": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "boolean"}}, "installWithPwd": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "boolean"}}, "installPwd": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "appLevel": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "ownerId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}}}, "changelog": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "updateMode": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "enum", "entries": {"silent": "silent", "normal": "normal", "force": "force"}}, "enum": {"silent": "silent", "normal": "normal", "force": "force"}, "options": ["silent", "normal", "force"]}, "releaseVersionCode": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "releaseVersionId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}}}, "grayReleaseVersionId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}}}, "totalDownloadCount": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "dailyStats": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "array", "element": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "object", "shape": {"id": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "appId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "statDate": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}, "downloadCount": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "viewCount": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "createdAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}}}}}, "element": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "object", "shape": {"id": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "appId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "statDate": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}, "downloadCount": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "viewCount": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "createdAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}}}}}}}, "grayStrategies": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "array", "element": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "object", "shape": {"id": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "appId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "name": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "description": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "ipType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "enum", "entries": {"white": "white", "black": "black"}}, "enum": {"white": "white", "black": "black"}, "options": ["white", "black"]}, "count": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}}}, "ipList": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "array", "element": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}, "element": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}}, "downloadCountLimit": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}}}, "updateMode": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "enum", "entries": {"silent": "silent", "normal": "normal", "force": "force"}}, "enum": {"silent": "silent", "normal": "normal", "force": "force"}, "options": ["silent", "normal", "force"]}, "active": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "boolean"}}, "createdAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}}}}}, "element": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "object", "shape": {"id": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "appId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "name": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "description": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "ipType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "enum", "entries": {"white": "white", "black": "black"}}, "enum": {"white": "white", "black": "black"}, "options": ["white", "black"]}, "count": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}}}, "ipList": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "array", "element": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}, "element": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}}, "downloadCountLimit": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}}}, "updateMode": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "enum", "entries": {"silent": "silent", "normal": "normal", "force": "force"}}, "enum": {"silent": "silent", "normal": "normal", "force": "force"}, "options": ["silent", "normal", "force"]}, "active": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "boolean"}}, "createdAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}}}}}}}, "createdAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}, "updatedAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}}}}, "BuildInfo": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "object", "shape": {"id": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "appId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "platform": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "bundleId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "buildNo": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "uploadAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}, "createdAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}, "updatedAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}}}}, "DownloadInfo": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "object", "shape": {"id": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "appId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "versionId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}}}, "userId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}}}, "clientIp": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "userAgent": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "downloadType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "remark": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "image": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "param": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "page": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "createdAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}, "updatedAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}}}}, "InviteInfo": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "object", "shape": {"id": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "userName": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "teamId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "type": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "enum", "entries": {"TEAM": "TEAM"}}, "enum": {"TEAM": "TEAM"}, "options": ["TEAM"]}, "status": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "enum", "entries": {"EBL": "EBL", "DBL": "DBL"}}, "enum": {"EBL": "EBL", "DBL": "DBL"}, "options": ["EBL", "DBL"]}, "emails": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "array", "element": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "object", "shape": {"id": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "inviteId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "email": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string", "checks": [{"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string", "format": "email", "check": "string_format", "abort": false, "pattern": {}}, "format": "email", "minLength": null, "maxLength": null}]}, "format": "email", "minLength": null, "maxLength": null}, "status": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "enum", "entries": {"EBL": "EBL", "DBL": "DBL"}}, "enum": {"EBL": "EBL", "DBL": "DBL"}, "options": ["EBL", "DBL"]}, "createdAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}, "updatedAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}}}}}, "element": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "object", "shape": {"id": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "inviteId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "email": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string", "checks": [{"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string", "format": "email", "check": "string_format", "abort": false, "pattern": {}}, "format": "email", "minLength": null, "maxLength": null}]}, "format": "email", "minLength": null, "maxLength": null}, "status": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "enum", "entries": {"EBL": "EBL", "DBL": "DBL"}}, "enum": {"EBL": "EBL", "DBL": "DBL"}, "options": ["EBL", "DBL"]}, "createdAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}, "updatedAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}}}}}}}, "createdAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}, "updatedAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}}}}, "MessageInfo": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "object", "shape": {"id": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "category": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "content": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "senderId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}}}, "receiverId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "sendAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}, "status": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "data": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "createdAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}, "updatedAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}}}}, "MiniappInfo": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "object", "shape": {"id": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "appName": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "appId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "platform": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "pagePath": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "appEnv": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "boolean"}}, "ownerId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}}}, "creatorId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}}}, "creator": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "appSecret": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "icon": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "describe": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "changelog": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "downloadCodeImages": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "array", "element": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "object", "shape": {"id": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "miniappId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "type": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "image": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "env": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "developer": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "version": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "desc": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "pagePath": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "searchQuery": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "createdAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}, "updatedAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}}}}}, "element": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "object", "shape": {"id": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "miniappId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "type": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "image": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "env": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "developer": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "version": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "desc": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "pagePath": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "searchQuery": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "createdAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}, "updatedAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}}}}}}}, "createdAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}, "updatedAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}}}}, "TeamInfo": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "object", "shape": {"id": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "name": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "icon": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "isDefault": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "boolean"}}, "creatorId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}}}, "creatorName": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "description": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "memberCount": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "status": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "settings": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "record", "keyType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "valueType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "unknown"}}}, "keyType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "valueType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "unknown"}}}}}, "appCount": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "members": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "array", "element": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "object", "shape": {"id": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "teamId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "userId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "username": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "email": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "role": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "enum", "entries": {"owner": "owner", "manager": "manager", "guest": "guest"}}, "enum": {"owner": "owner", "manager": "manager", "guest": "guest"}, "options": ["owner", "manager", "guest"]}, "userAvatar": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "joinedAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}, "invitedBy": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "invitedById": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}}}, "status": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "note": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "createdAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}, "updatedAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}}}}}, "element": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "object", "shape": {"id": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "teamId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "userId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "username": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "email": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "role": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "enum", "entries": {"owner": "owner", "manager": "manager", "guest": "guest"}}, "enum": {"owner": "owner", "manager": "manager", "guest": "guest"}, "options": ["owner", "manager", "guest"]}, "userAvatar": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "joinedAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}, "invitedBy": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "invitedById": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}}}, "status": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "note": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "createdAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}, "updatedAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}}}}}}}, "createdAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}, "updatedAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}}}}, "UserInfo": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "object", "shape": {"id": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "username": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "userAvatar": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "userAvatarHistory": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "array", "element": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}, "element": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}}, "email": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string", "checks": [{"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string", "format": "email", "check": "string_format", "abort": false, "pattern": {}}, "format": "email", "minLength": null, "maxLength": null}]}, "format": "email", "minLength": null, "maxLength": null}, "token": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "apiToken": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "nullable", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "mobile": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "qq": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "company": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "career": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "userTeams": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "array", "element": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "object", "shape": {"id": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "userId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "teamId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "teamName": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "teamIcon": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "role": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "enum", "entries": {"owner": "owner", "manager": "manager", "guest": "guest"}}, "enum": {"owner": "owner", "manager": "manager", "guest": "guest"}, "options": ["owner", "manager", "guest"]}, "createdAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}}}}}, "element": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "object", "shape": {"id": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "userId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "teamId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "teamName": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "teamIcon": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "role": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "enum", "entries": {"owner": "owner", "manager": "manager", "guest": "guest"}}, "enum": {"owner": "owner", "manager": "manager", "guest": "guest"}, "options": ["owner", "manager", "guest"]}, "createdAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}}}}}}}, "createdAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}, "updatedAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}}}}, "VersionInfo": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "object", "shape": {"id": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "appId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "bundleId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "icon": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "versionStr": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "versionCode": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "uploadAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}, "uploader": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "uploaderId": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}}}, "size": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "active": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "boolean"}}, "downloadUrl": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "downloadCount": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "fileDownloadUrl": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "installUrl": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "showOnDownloadPage": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "boolean"}}, "appLevel": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "changelog": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "md5": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "hidden": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "boolean"}}, "updateMode": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "enum", "entries": {"silent": "silent", "normal": "normal", "force": "force"}}, "enum": {"silent": "silent", "normal": "normal", "force": "force"}, "options": ["silent", "normal", "force"]}, "released": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "boolean"}}, "versionTag": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "versionNote": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "sortWeight": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}, "status": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "minOsVersion": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}, "supportedDevices": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "array", "element": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}, "element": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}}}}, "metadata": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "record", "keyType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "valueType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "unknown"}}}, "keyType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "string"}, "format": null, "minLength": null, "maxLength": null}, "valueType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "unknown"}}}}}, "releasedAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}}}, "buildNumber": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "optional", "innerType": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "number", "checks": []}, "minValue": null, "maxValue": null, "isInt": false, "isFinite": true, "format": null}}}, "createdAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}, "updatedAt": {"~standard": {"vendor": "zod", "version": 1}, "def": {"type": "date"}, "minDate": null, "maxDate": null}}}}, "LoginRequest": {"type": "object", "properties": {"username": {"type": "string", "minLength": 3, "maxLength": 20, "description": "用户名", "example": "admin"}, "password": {"type": "string", "minLength": 6, "maxLength": 20, "description": "密码", "example": "123456"}}, "required": ["username", "password"]}, "LoginVO": {"type": "object", "properties": {"user": {"type": "object", "properties": {"id": {"type": "number", "example": 1}, "username": {"type": "string", "example": "admin"}, "nickname": {"type": "string", "example": "管理员"}, "avatar": {"type": "string", "example": "https://example.com/avatar.png"}, "role": {"type": "string", "enum": ["admin", "user"], "example": "admin"}}, "required": ["id", "username", "role"]}, "token": {"type": "string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "description": "JWT 访问令牌"}}, "required": ["user", "token"]}, "GetUserInfoVO": {"type": "object", "properties": {"code": {"type": "number", "example": 0}, "data": {"type": "object", "properties": {"id": {"type": "number", "example": 1}, "username": {"type": "string", "example": "admin"}, "nickname": {"type": "string", "example": "管理员"}, "avatar": {"type": "string", "example": "https://example.com/avatar.png"}, "role": {"type": "string", "enum": ["admin", "user"], "example": "admin"}}, "required": ["id", "username", "role"]}, "message": {"type": "string", "example": "获取成功"}}, "required": ["code", "data", "message"]}, "GetHealthVO": {"type": "object", "properties": {"status": {"type": "string", "example": "ok", "description": "服务状态"}, "timestamp": {"type": "string", "example": "2025-07-21T09:28:18.455Z", "description": "时间戳"}, "db": {"anyOf": [{"type": "string"}, {"nullable": true}, {"nullable": true}], "example": "ok", "description": "数据库状态"}}, "required": ["status", "timestamp"]}}}, "securityDefinitions": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "Authorization"}}}