{"openapi": "3.0.0", "info": {"title": "CFE Node Server API", "version": "1.0.0", "description": "基于 Node.js + Koa + TypeScript 的后端服务框架"}, "paths": {"/api/user/login": {"post": {"summary": "用户登录", "description": "用户登录接口，返回用户信息和 JWT token", "operationId": "login", "tags": ["用户管理"], "parameters": [{"name": "Authorization", "in": "header", "required": false, "schema": {"type": "string"}, "description": "Bearer token，登录后获取"}, {"required": true, "schema": {"type": "string", "enum": ["application/json"]}, "description": "请求内容类型，必须为 application/json", "name": "Content-Type", "in": "header"}, {"required": false, "schema": {"type": "string"}, "description": "用户代理信息", "name": "User-Agent", "in": "header"}, {"required": false, "schema": {"type": "string"}, "description": "请求追踪ID", "name": "X-Request-ID", "in": "header"}], "methodName": "login", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "登录成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_LoginVO_"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_StringData_"}}}}, "401": {"description": "用户名或密码错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_StringData_"}}}}, "429": {"description": "请求过于频繁", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_StringData_"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_StringData_"}}}}}}}, "/api/user/info": {"get": {"summary": "获取用户信息", "description": "获取当前登录用户的详细信息", "operationId": "getUserInfo", "tags": ["用户管理"], "parameters": [{"name": "Authorization", "in": "header", "required": false, "schema": {"type": "string"}, "description": "Bearer token，登录后获取"}, {"required": true, "schema": {"type": "string", "pattern": "^Bearer .+"}, "description": "Bearer token，格式: Bearer <token>", "name": "Authorization", "in": "header"}, {"required": false, "schema": {"type": "string"}, "description": "请求追踪ID", "name": "X-Request-ID", "in": "header"}], "methodName": "getUserInfo", "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_GetUserInfoVO_"}}}}, "401": {"description": "未授权，token 无效或已过期", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_StringData_"}}}}, "403": {"description": "权限不足", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_StringData_"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_StringData_"}}}}}}}, "/api/health": {"get": {"summary": "健康检查", "description": "服务健康检查接口", "operationId": "getHealth", "tags": ["系统健康检查"], "parameters": [], "methodName": "getHealth", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_GetHealthVO_"}}}}}}}}, "components": {"parameters": {"0": {"name": "Authorization", "in": "header", "required": false, "schema": {"type": "string"}, "description": "Bearer token，登录后获取"}}, "schemas": {"R_LoginVO_": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/LoginVO"}, "resultCode": {"type": "number", "example": 200}, "resultMessage": {"type": "string", "example": "success"}}, "required": ["resultCode", "resultMessage", "data"]}, "R_GetUserInfoVO_": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/GetUserInfoVO"}, "resultCode": {"type": "number", "example": 200}, "resultMessage": {"type": "string", "example": "success"}}, "required": ["resultCode", "resultMessage", "data"]}, "R_GetHealthVO_": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/GetHealthVO"}, "resultCode": {"type": "number", "example": 200}, "resultMessage": {"type": "string", "example": "success"}}, "required": ["resultCode", "resultMessage", "data"]}, "R_StringData_": {"type": "object", "properties": {"resultCode": {"type": "number", "example": 200}, "resultMessage": {"type": "string", "example": "success"}, "data": {"type": "string", "example": ""}}, "required": ["resultCode", "resultMessage", "data"]}, "StringData": {"type": "string", "example": ""}, "AppInfo": {"type": "object", "properties": {"id": {"type": "number", "description": "应用ID"}, "platform": {"type": "string", "description": "平台类型", "example": "iOS"}, "bundleId": {"type": "string", "description": "包标识符", "example": "com.example.app"}, "bundleName": {"type": "string", "description": "包名称"}, "appName": {"type": "string", "description": "应用名称", "example": "Example App"}, "currentVersion": {"type": "string", "description": "当前版本", "example": "1.0.0"}, "creatorId": {"type": "number", "description": "创建者ID"}, "creator": {"type": "string", "description": "创建者名称"}, "icon": {"type": "string", "description": "应用图标URL"}, "describe": {"type": "string", "description": "应用描述"}, "shortUrl": {"type": "string", "description": "短链接"}, "autoPublish": {"type": "boolean", "description": "是否自动发布"}, "installWithPwd": {"type": "boolean", "description": "安装是否需要密码"}, "installPwd": {"type": "string", "description": "安装密码"}, "appLevel": {"type": "string", "description": "应用等级"}, "ownerId": {"type": "number", "description": "拥有者ID"}, "changelog": {"type": "string", "description": "更新日志"}, "updateMode": {"type": "string", "enum": ["silent", "normal", "force"], "description": "更新模式"}, "releaseVersionCode": {"type": "string", "description": "发布版本代码"}, "releaseVersionId": {"type": "number", "description": "发布版本ID"}, "grayReleaseVersionId": {"type": "number", "description": "灰度版本ID"}, "totalDownloadCount": {"type": "number", "description": "总下载次数"}, "dailyStats": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number", "description": "统计ID"}, "appId": {"type": "number", "description": "应用ID"}, "statDate": {"type": "string", "format": "date", "description": "统计日期"}, "downloadCount": {"type": "number", "description": "下载次数"}, "viewCount": {"type": "number", "description": "查看次数"}, "createdAt": {"type": "string", "format": "date", "description": "创建时间"}}, "required": ["id", "appId", "statDate", "downloadCount", "viewCount", "createdAt"]}, "description": "每日统计"}, "grayStrategies": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number", "description": "策略ID"}, "appId": {"type": "number", "description": "应用ID"}, "name": {"type": "string", "description": "策略名称"}, "description": {"type": "string", "description": "策略描述"}, "ipType": {"type": "string", "enum": ["white", "black"], "description": "IP类型"}, "count": {"type": "number", "description": "用户数量限制"}, "ipList": {"type": "array", "items": {"type": "string"}, "description": "IP列表"}, "downloadCountLimit": {"type": "number", "description": "下载次数限制"}, "updateMode": {"type": "string", "enum": ["silent", "normal", "force"], "description": "更新模式"}, "active": {"type": "boolean", "description": "是否启用"}, "createdAt": {"type": "string", "format": "date", "description": "创建时间"}}, "required": ["id", "appId", "ipType", "updateMode", "active", "createdAt"]}, "description": "灰度策略"}, "createdAt": {"type": "string", "format": "date", "description": "创建时间"}, "updatedAt": {"type": "string", "format": "date", "description": "更新时间"}}, "required": ["id", "platform", "bundleId", "bundleName", "appName", "currentVersion", "autoPublish", "installWithPwd", "appLevel", "updateMode", "totalDownloadCount", "createdAt", "updatedAt"]}, "BuildInfo": {"type": "object", "properties": {"id": {"type": "number", "description": "构建ID"}, "appId": {"type": "number", "description": "应用ID"}, "platform": {"type": "string", "description": "平台类型", "example": "iOS"}, "bundleId": {"type": "string", "description": "包标识符", "example": "com.example.app"}, "buildNo": {"type": "number", "description": "构建编号", "example": 1001}, "uploadAt": {"type": "string", "format": "date", "description": "上传时间"}, "createdAt": {"type": "string", "format": "date", "description": "创建时间"}, "updatedAt": {"type": "string", "format": "date", "description": "更新时间"}}, "required": ["id", "appId", "platform", "bundleId", "buildNo", "uploadAt", "createdAt", "updatedAt"]}, "DownloadInfo": {"type": "object", "properties": {"id": {"type": "number", "description": "下载记录ID"}, "appId": {"type": "number", "description": "应用ID"}, "versionId": {"type": "number", "description": "版本ID"}, "userId": {"type": "number", "description": "用户ID"}, "clientIp": {"type": "string", "description": "客户端IP", "example": "***********"}, "userAgent": {"type": "string", "description": "用户代理"}, "downloadType": {"type": "string", "description": "下载类型", "example": "web"}, "remark": {"type": "string", "description": "备注"}, "image": {"type": "string", "description": "下载页面图片"}, "param": {"type": "string", "description": "下载参数"}, "page": {"type": "string", "description": "下载页面URL"}, "createdAt": {"type": "string", "format": "date", "description": "下载时间"}, "updatedAt": {"type": "string", "format": "date", "description": "更新时间"}}, "required": ["id", "appId", "downloadType", "createdAt", "updatedAt"]}, "InviteInfo": {"type": "object", "properties": {"id": {"type": "number", "description": "邀请ID"}, "userName": {"type": "string", "description": "邀请人名称", "example": "<PERSON>"}, "teamId": {"type": "number", "description": "团队ID"}, "type": {"type": "string", "enum": ["TEAM"], "description": "邀请类型"}, "status": {"type": "string", "enum": ["EBL", "DBL"], "description": "邀请状态"}, "emails": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number", "description": "邮箱状态ID"}, "inviteId": {"type": "number", "description": "邀请ID"}, "email": {"type": "string", "format": "email", "description": "邀请邮箱", "example": "<EMAIL>"}, "status": {"type": "string", "enum": ["EBL", "DBL"], "description": "邀请状态"}, "createdAt": {"type": "string", "format": "date", "description": "创建时间"}, "updatedAt": {"type": "string", "format": "date", "description": "更新时间"}}, "required": ["id", "inviteId", "email", "status", "createdAt", "updatedAt"]}, "description": "邀请邮箱列表"}, "createdAt": {"type": "string", "format": "date", "description": "创建时间"}, "updatedAt": {"type": "string", "format": "date", "description": "更新时间"}}, "required": ["id", "userName", "teamId", "type", "status", "createdAt", "updatedAt"]}, "MessageInfo": {"type": "object", "properties": {"id": {"type": "number", "description": "消息ID"}, "category": {"type": "string", "description": "消息类型", "example": "INVITE"}, "content": {"type": "string", "description": "消息内容"}, "senderId": {"type": "number", "description": "发送者ID"}, "receiverId": {"type": "number", "description": "接收者ID"}, "sendAt": {"type": "string", "format": "date", "description": "发送时间"}, "status": {"type": "string", "description": "消息状态", "example": "pending"}, "data": {"type": "string", "description": "附加数据"}, "createdAt": {"type": "string", "format": "date", "description": "创建时间"}, "updatedAt": {"type": "string", "format": "date", "description": "更新时间"}}, "required": ["id", "receiverId", "sendAt", "status", "createdAt", "updatedAt"]}, "MiniappInfo": {"type": "object", "properties": {"id": {"type": "number", "description": "小程序主键ID"}, "appName": {"type": "string", "description": "小程序名称", "example": "示例小程序"}, "appId": {"type": "string", "description": "小程序ID", "example": "wx*********0abcdef"}, "platform": {"type": "string", "description": "平台类型", "example": "wechat"}, "pagePath": {"type": "string", "description": "页面路径"}, "appEnv": {"type": "boolean", "description": "应用环境"}, "ownerId": {"type": "number", "description": "拥有者ID"}, "creatorId": {"type": "number", "description": "创建者ID"}, "creator": {"type": "string", "description": "创建者名称"}, "appSecret": {"type": "string", "description": "小程序密钥"}, "icon": {"type": "string", "description": "小程序图标"}, "describe": {"type": "string", "description": "小程序描述"}, "changelog": {"type": "string", "description": "更新日志"}, "downloadCodeImages": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number", "description": "下载码ID"}, "miniappId": {"type": "number", "description": "小程序ID"}, "type": {"type": "string", "description": "下载码类型", "example": "qrcode"}, "image": {"type": "string", "description": "下载码图片URL"}, "env": {"type": "string", "description": "环境标识", "example": "production"}, "developer": {"type": "string", "description": "开发者名称"}, "version": {"type": "string", "description": "版本号", "example": "1.0.0"}, "desc": {"type": "string", "description": "描述信息"}, "pagePath": {"type": "string", "description": "页面路径"}, "searchQuery": {"type": "string", "description": "搜索查询参数"}, "createdAt": {"type": "string", "format": "date", "description": "创建时间"}, "updatedAt": {"type": "string", "format": "date", "description": "更新时间"}}, "required": ["id", "miniappId", "type", "image", "env", "createdAt", "updatedAt"]}, "description": "下载码列表"}, "createdAt": {"type": "string", "format": "date", "description": "创建时间"}, "updatedAt": {"type": "string", "format": "date", "description": "更新时间"}}, "required": ["id", "appName", "appId", "platform", "pagePath", "appEnv", "createdAt", "updatedAt"]}, "TeamInfo": {"type": "object", "properties": {"id": {"type": "number", "description": "团队ID"}, "name": {"type": "string", "description": "团队名称", "example": "开发团队"}, "icon": {"type": "string", "description": "团队图标URL", "example": "https://example.com/team-icon.png"}, "isDefault": {"type": "boolean", "description": "是否为默认团队", "example": false}, "creatorId": {"type": "number", "description": "创建者ID"}, "creatorName": {"type": "string", "description": "创建者名称"}, "description": {"type": "string", "description": "团队描述"}, "memberCount": {"type": "number", "description": "成员数量", "example": 5}, "status": {"type": "string", "description": "团队状态"}, "settings": {"type": "object", "additionalProperties": {"nullable": true}, "description": "团队设置"}, "appCount": {"type": "number", "description": "应用数量"}, "members": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number", "description": "成员关系ID"}, "teamId": {"type": "number", "description": "团队ID"}, "userId": {"type": "number", "description": "用户ID"}, "username": {"type": "string", "description": "用户名"}, "email": {"type": "string", "description": "用户邮箱"}, "role": {"type": "string", "enum": ["owner", "manager", "guest"], "description": "团队角色", "example": "guest"}, "userAvatar": {"type": "string", "description": "用户头像"}, "joinedAt": {"type": "string", "format": "date", "description": "加入时间"}, "invitedBy": {"type": "string", "description": "邀请者"}, "invitedById": {"type": "number", "description": "邀请者ID"}, "status": {"type": "string", "description": "成员状态"}, "note": {"type": "string", "description": "成员备注"}, "createdAt": {"type": "string", "format": "date", "description": "创建时间"}, "updatedAt": {"type": "string", "format": "date", "description": "更新时间"}}, "required": ["id", "teamId", "userId", "role", "joinedAt", "status", "createdAt", "updatedAt"]}, "description": "团队成员列表"}, "createdAt": {"type": "string", "format": "date", "description": "创建时间"}, "updatedAt": {"type": "string", "format": "date", "description": "更新时间"}}, "required": ["id", "name", "isDefault", "memberCount", "appCount", "createdAt", "updatedAt"]}, "UserInfo": {"type": "object", "properties": {"id": {"type": "number", "description": "用户ID"}, "username": {"type": "string", "description": "用户名", "example": "john_doe"}, "userAvatar": {"type": "string", "description": "用户头像URL", "example": "https://example.com/avatar.jpg"}, "userAvatarHistory": {"type": "array", "items": {"type": "string"}, "description": "头像历史记录"}, "email": {"type": "string", "format": "email", "description": "邮箱地址", "example": "<EMAIL>"}, "token": {"type": "string", "description": "认证令牌"}, "apiToken": {"type": "string", "nullable": true, "description": "API 访问令牌"}, "mobile": {"type": "string", "description": "手机号码", "example": "13800138000"}, "qq": {"type": "string", "description": "QQ 号码", "example": "*********"}, "company": {"type": "string", "description": "公司名称", "example": "Example Corp"}, "career": {"type": "string", "description": "职业/职位", "example": "前端开发工程师"}, "userTeams": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number", "description": "关系ID"}, "userId": {"type": "number", "description": "用户ID"}, "teamId": {"type": "number", "description": "团队ID"}, "teamName": {"type": "string", "description": "团队名称"}, "teamIcon": {"type": "string", "description": "团队图标"}, "role": {"type": "string", "enum": ["owner", "manager", "guest"], "description": "用户角色", "example": "guest"}, "createdAt": {"type": "string", "format": "date", "description": "加入时间"}}, "required": ["id", "userId", "teamId", "role", "createdAt"]}, "description": "用户的团队关系"}, "createdAt": {"type": "string", "format": "date", "description": "创建时间"}, "updatedAt": {"type": "string", "format": "date", "description": "更新时间"}}, "required": ["id", "username", "userAvatar", "email", "token", "apiToken", "mobile", "qq", "company", "career", "createdAt", "updatedAt"]}, "VersionInfo": {"type": "object", "properties": {"id": {"type": "number", "description": "版本ID"}, "appId": {"type": "number", "description": "应用ID"}, "bundleId": {"type": "string", "description": "包标识符"}, "icon": {"type": "string", "description": "版本图标URL"}, "versionStr": {"type": "string", "description": "版本字符串", "example": "1.0.0"}, "versionCode": {"type": "string", "description": "版本代码", "example": "100"}, "uploadAt": {"type": "string", "format": "date", "description": "上传时间"}, "uploader": {"type": "string", "description": "上传者名称"}, "uploaderId": {"type": "number", "description": "上传者ID"}, "size": {"type": "number", "description": "文件大小(字节)"}, "active": {"type": "boolean", "description": "是否激活"}, "downloadUrl": {"type": "string", "description": "下载URL"}, "downloadCount": {"type": "number", "description": "下载次数"}, "fileDownloadUrl": {"type": "string", "description": "文件下载URL"}, "installUrl": {"type": "string", "description": "安装URL"}, "showOnDownloadPage": {"type": "boolean", "description": "是否在下载页显示"}, "appLevel": {"type": "string", "description": "应用等级"}, "changelog": {"type": "string", "description": "更新日志"}, "md5": {"type": "string", "description": "文件MD5值"}, "hidden": {"type": "boolean", "description": "是否隐藏"}, "updateMode": {"type": "string", "enum": ["silent", "normal", "force"], "description": "更新模式"}, "released": {"type": "boolean", "description": "是否已发布"}, "versionTag": {"type": "string", "description": "版本标签"}, "versionNote": {"type": "string", "description": "版本备注"}, "sortWeight": {"type": "number", "description": "排序权重"}, "status": {"type": "string", "description": "版本状态"}, "minOsVersion": {"type": "string", "description": "最低系统版本"}, "supportedDevices": {"type": "array", "items": {"type": "string"}, "description": "支持设备"}, "metadata": {"type": "object", "additionalProperties": {"nullable": true}, "description": "版本元数据"}, "releasedAt": {"type": "string", "format": "date", "description": "发布时间"}, "buildNumber": {"type": "number", "description": "构建号"}, "createdAt": {"type": "string", "format": "date", "description": "创建时间"}, "updatedAt": {"type": "string", "format": "date", "description": "更新时间"}}, "required": ["id", "appId", "uploadAt", "size", "active", "downloadCount", "showOnDownloadPage", "hidden", "updateMode", "released", "sortWeight", "status", "createdAt", "updatedAt"]}, "LoginRequest": {"type": "object", "properties": {"username": {"type": "string", "minLength": 3, "maxLength": 20, "description": "用户名", "example": "admin"}, "password": {"type": "string", "minLength": 6, "maxLength": 20, "description": "密码", "example": "123456"}}, "required": ["username", "password"]}, "LoginVO": {"type": "object", "properties": {"id": {"type": "number", "description": "用户ID"}, "username": {"type": "string", "description": "用户名", "example": "john_doe"}, "userAvatar": {"type": "string", "description": "用户头像URL", "example": "https://example.com/avatar.jpg"}, "userAvatarHistory": {"type": "array", "items": {"type": "string"}, "description": "头像历史记录"}, "email": {"type": "string", "format": "email", "description": "邮箱地址", "example": "<EMAIL>"}, "token": {"type": "string", "description": "认证令牌"}, "apiToken": {"type": "string", "nullable": true, "description": "API 访问令牌"}, "mobile": {"type": "string", "description": "手机号码", "example": "13800138000"}, "qq": {"type": "string", "description": "QQ 号码", "example": "*********"}, "company": {"type": "string", "description": "公司名称", "example": "Example Corp"}, "career": {"type": "string", "description": "职业/职位", "example": "前端开发工程师"}, "userTeams": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number", "description": "关系ID"}, "userId": {"type": "number", "description": "用户ID"}, "teamId": {"type": "number", "description": "团队ID"}, "teamName": {"type": "string", "description": "团队名称"}, "teamIcon": {"type": "string", "description": "团队图标"}, "role": {"type": "string", "enum": ["owner", "manager", "guest"], "description": "用户角色", "example": "guest"}, "createdAt": {"type": "string", "format": "date", "description": "加入时间"}}, "required": ["id", "userId", "teamId", "role", "createdAt"]}, "description": "用户的团队关系"}, "createdAt": {"type": "string", "format": "date", "description": "创建时间"}, "updatedAt": {"type": "string", "format": "date", "description": "更新时间"}}, "required": ["id", "username", "userAvatar", "email", "token", "apiToken", "mobile", "qq", "company", "career", "createdAt", "updatedAt"]}, "GetUserInfoVO": {"type": "object", "properties": {"code": {"type": "number", "example": 0}, "data": {"type": "object", "properties": {"id": {"type": "number", "example": 1}, "username": {"type": "string", "example": "admin"}, "nickname": {"type": "string", "example": "管理员"}, "avatar": {"type": "string", "example": "https://example.com/avatar.png"}, "role": {"type": "string", "enum": ["admin", "user"], "example": "admin"}}, "required": ["id", "username", "role"]}, "message": {"type": "string", "example": "获取成功"}}, "required": ["code", "data", "message"]}, "GetHealthVO": {"type": "object", "properties": {"status": {"type": "string", "example": "ok", "description": "服务状态"}, "timestamp": {"type": "string", "example": "2025-07-22T00:18:10.487Z", "description": "时间戳"}, "db": {"anyOf": [{"type": "string"}, {"nullable": true}, {"nullable": true}], "example": "ok", "description": "数据库状态"}}, "required": ["status", "timestamp"]}}}, "securityDefinitions": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "Authorization"}}}