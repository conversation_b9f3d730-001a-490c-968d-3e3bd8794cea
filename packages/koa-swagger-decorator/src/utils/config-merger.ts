/**
 * 配置合并策略
 * 提供智能的配置合并功能，处理默认配置和用户配置的合并
 */

import type { SwaggerRouterConfig, SwaggerUIConfig } from '../types'
import deepmerge from 'deepmerge'
import { validateSwaggerRouterConfig } from '../config/schema'
import { logger } from './logger'

/**
 * 默认 Swagger 路由器配置
 */
export const defaultSwaggerRouterConfig: SwaggerRouterConfig = {
  swaggerJsonEndpoint: '/swagger.json',
  swaggerHtmlEndpoint: '/swagger',
  validateRequest: true,
  validateResponse: false,
  spec: {
    openapi: '3.0.0',
    info: {
      title: 'Swagger API',
      version: '1.0.0',
      description: 'API documentation generated by koa-swagger-decorator',
    },
    components: {
      schemas: {},
    },
  },
  swaggerUIConfig: {
    routePrefix: '/swagger',
    title: 'Swagger API',
    hideTopbar: false,
    swaggerOptions: {
      docExpansion: 'list',
      persistAuthorization: true,
      supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch', 'head', 'options'],
      defaultModelRendering: 'schema',
    },
  },
}

/**
 * 默认 Swagger UI 配置
 */
export const defaultSwaggerUIConfig: SwaggerUIConfig = {
  routePrefix: '/swagger',
  title: 'Swagger API',
  hideTopbar: false,
  swaggerOptions: {
    docExpansion: 'list',
    persistAuthorization: true,
    supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch', 'head', 'options'],
    defaultModelRendering: 'schema',
  },
}

/**
 * 合并配置选项
 */
export interface MergeConfigOptions {
  /** 是否验证配置 */
  validate?: boolean
  /** 是否处理特殊配置项关联 */
  handleSpecialRelations?: boolean
  /** 是否记录日志 */
  logging?: boolean
}

/**
 * 合并 Swagger 路由器配置
 * @param userConfig 用户配置
 * @param options 合并选项
 * @returns 合并后的配置
 */
export function mergeSwaggerRouterConfig(
  userConfig: Partial<SwaggerRouterConfig> = {},
  options: MergeConfigOptions = {},
): SwaggerRouterConfig {
  const { validate = true, handleSpecialRelations = true, logging = true } = options

  // 验证用户配置
  if (validate) {
    const validationResult = validateSwaggerRouterConfig(userConfig)
    if (!validationResult.valid) {
      if (logging) {
        logger.warn('Invalid SwaggerRouter configuration:', validationResult.errors)
      }

      // 过滤掉无效的配置项
      Object.keys(userConfig).forEach((key) => {
        if (validationResult.errors?.some((err) => err.startsWith(key))) {
          if (logging) {
            logger.warn(`Removing invalid config key: ${key}`)
          }
          delete userConfig[key as keyof SwaggerRouterConfig]
        }
      })
    }
  }

  // 合并配置
  const mergedConfig = deepmerge(defaultSwaggerRouterConfig, userConfig, {
    // 数组合并策略：用户配置覆盖默认配置
    arrayMerge: (_target, source) => source,
  }) as SwaggerRouterConfig

  // 处理特殊配置项关联
  if (handleSpecialRelations) {
    // 确保 swaggerHtmlEndpoint 与 swaggerUIConfig.routePrefix 一致
    if (userConfig.swaggerUIConfig?.routePrefix && !userConfig.swaggerHtmlEndpoint) {
      mergedConfig.swaggerHtmlEndpoint = userConfig.swaggerUIConfig.routePrefix
    }

    // 如果用户提供了 swaggerHtmlEndpoint 但没有提供 swaggerUIConfig.routePrefix
    if (userConfig.swaggerHtmlEndpoint && !userConfig.swaggerUIConfig?.routePrefix) {
      if (!mergedConfig.swaggerUIConfig) {
        mergedConfig.swaggerUIConfig = { ...defaultSwaggerUIConfig }
      }
      mergedConfig.swaggerUIConfig.routePrefix = userConfig.swaggerHtmlEndpoint
    }
  }

  if (logging) {
    logger.debug('Merged SwaggerRouter configuration:', mergedConfig)
  }

  return mergedConfig
}

/**
 * 合并 Swagger UI 配置
 * @param userConfig 用户配置
 * @returns 合并后的配置
 */
export function mergeSwaggerUIConfig(
  userConfig: Partial<SwaggerUIConfig> = {},
): SwaggerUIConfig {
  return deepmerge(defaultSwaggerUIConfig, userConfig, {
    arrayMerge: (_target, source) => source,
  }) as SwaggerUIConfig
}
