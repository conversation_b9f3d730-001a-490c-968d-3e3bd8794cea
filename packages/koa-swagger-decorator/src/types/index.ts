/**
 * @fileoverview Swagger 装饰器类型定义系统
 *
 * 提供完整的 TypeScript 类型定义，包括：
 * - HTTP 相关类型定义
 * - 控制器和路由配置类型
 * - OpenAPI 规范相关类型
 * - 装饰器配置类型
 * - 类型守卫函数
 *
 * 这些类型定义确保了：
 * - 编译时类型安全
 * - 更好的 IDE 智能提示
 * - 运行时类型检查支持
 * - API 文档自动生成
 *
 * <AUTHOR>
 * @since 2025-07-16
 */

import type { OpenAPIRegistry } from '@asteasolutions/zod-to-openapi'
import type { OpenAPIObjectConfig } from '@asteasolutions/zod-to-openapi/dist/v3.0/openapi-generator'
import type { RouterOptions } from '@koa/router'
import type { Context, Middleware } from 'koa'
import type { KoaSwaggerUiOptions } from 'koa2-swagger-ui'
import type { HeaderObject, OpenAPIObject, ParameterObject, ReferenceObject, ResponseObject, SchemaObject } from 'openapi3-ts/oas30'
import type { ZodObject, ZodTypeAny } from 'zod'

/**
 * HTTP 方法类型
 *
 * 支持的 HTTP 方法枚举，符合 REST API 标准
 *
 * @example
 * ```typescript
 * const method: HttpMethod = 'post'
 * const methods: HttpMethod[] = ['get', 'post', 'put', 'delete']
 * ```
 */
export type HttpMethod = 'get' | 'post' | 'put' | 'delete' | 'patch' | 'options' | 'head'

/**
 * 控制器类构造函数约束
 *
 * 定义了控制器类必须满足的结构要求：
 * - 必须是可实例化的构造函数
 * - 必须有 prototype 属性
 * - 必须有 name 属性
 *
 * @template T - 控制器实例类型
 *
 * @example
 * ```typescript
 * @Controller({ tags: ['用户管理'] })
 * class UserController {
 *   async getUser() { }
 * }
 *
 * // UserController 符合 ControllerClass<UserController> 约束
 * ```
 */
export interface ControllerClass<T = any> {
  /** 构造函数签名 */
  new(...args: any[]): T
  /** 原型对象，包含方法定义 */
  prototype: Record<string, any>
  /** 类名 */
  name: string
}

/**
 * 控制器配置接口
 *
 * 用于 @Controller 装饰器的配置参数，支持：
 * - 路径级别的参数定义
 * - OpenAPI 组件定义
 * - API 标签分类
 *
 * @example
 * ```typescript
 * @Controller({
 *   tags: ['用户管理', 'API v1'],
 *   paths: {
 *     parameters: [
 *       {
 *         name: 'version',
 *         in: 'header',
 *         required: true,
 *         schema: { type: 'string', enum: ['v1', 'v2'] }
 *       }
 *     ]
 *   },
 *   components: {
 *     parameters: {
 *       AuthToken: {
 *         name: 'Authorization',
 *         in: 'header',
 *         required: true,
 *         schema: { type: 'string' }
 *       }
 *     }
 *   }
 * })
 * class UserController { }
 * ```
 */
export interface ControllerConfig {
  /**
   * 路径配置
   *
   * 定义应用于控制器所有路由的通用配置
   */
  paths?: {
    /** 应用于所有路由的参数定义 */
    parameters?: (ParameterObject | ReferenceObject)[]
  }

  /**
   * OpenAPI 组件定义
   *
   * 定义可重用的 OpenAPI 组件，如参数、响应等
   */
  components?: {
    /** 可重用的参数定义 */
    parameters?: Record<string, ParameterObject | ReferenceObject>
    /** 可重用的 Schema 定义 */
    schemas?: Record<string, any>
  }

  /**
   * API 标签数组
   *
   * 用于在 Swagger UI 中对 API 进行分组和分类
   *
   * @example ['用户管理', '认证', 'API v1']
   */
  tags?: string[]
}

/**
 * 基础路由配置接口
 *
 * 定义路由的基本配置结构，包含 HTTP 方法、路径等核心信息
 *
 * @example
 * ```typescript
 * const routeConfig: IRouteConfig = {
 *   method: 'post',
 *   path: '/user/login',
 *   operationId: 'userLogin',
 *   summary: '用户登录',
 *   description: '使用用户名和密码进行登录认证'
 * }
 * ```
 */
export interface IRouteConfig {
  /**
   * HTTP 方法
   *
   * 支持的 REST API 方法：GET、POST、PUT、DELETE、PATCH、OPTIONS、HEAD
   */
  method: HttpMethod

  /**
   * 路由路径
   *
   * 支持路径参数，如：/user/:id 或 /user/{id}
   *
   * @example '/api/user/:id' 或 '/api/user/{id}'
   */
  path: string

  /**
   * 操作 ID
   *
   * OpenAPI 规范中的 operationId，用于唯一标识操作
   * 如果未提供，默认使用方法名
   *
   * @example 'getUserById', 'createUser'
   */
  operationId?: string

  /**
   * 接口摘要
   *
   * 简短的接口描述，显示在 Swagger UI 的接口列表中
   *
   * @example '获取用户信息', '创建新用户'
   */
  summary?: string

  /**
   * 接口详细描述
   *
   * 详细的接口说明，支持 Markdown 格式
   *
   * @example '根据用户ID获取用户的详细信息，包括基本信息和权限设置'
   */
  description?: string
  /** 接口标签 */
  tags?: string[]
  /** 路由参数 */
  parameters?: Record<string, ParameterObject | ReferenceObject>
  /** 请求体配置 */
  request?: {
    body?: {
      content: Record<string, any>
      required?: boolean
    }
  }
  /** 响应配置 */
  responses?: Record<string, ResponseObject>
  /** 是否已弃用 */
  deprecated?: boolean
  /** 安全配置 */
  security?: Array<Record<string, string[]>>
  /** 服务器配置 */
  servers?: Array<{ url: string, description?: string }>
  /** 外部文档 */
  externalDocs?: { url: string, description?: string }
}

// 扩展的路由配置类型
export interface StrongRouteConfig extends IRouteConfig {
  /** 类名 */
  className?: string
  /** 方法名 */
  methodName?: string
  /** 唯一标识符 */
  identifier?: string
  /** 其他扩展字段 */
  [key: string]: any
}

// 响应配置类型
export interface ResponseConfig {
  /** 响应描述 */
  description: string
  /** 响应 Schema */
  schema?: ZodTypeAny | { $ref: string } | string
  /** 响应头 */
  headers?: Record<string, ParameterObject>
}

// 响应配置映射类型
export interface ResponsesConfig {
  /** 状态码到响应配置的映射 */
  [statusCode: string]: ResponseConfig
}

// 请求头配置类型
export interface HeaderConfig {
  /** 请求头名称到配置的映射 */
  // [name: string]: RequiredKeys<ParameterObject, 'name' | 'in'>
  [name: string]: HeaderObject | ReferenceObject
}

// 中间件函数类型
export type MiddlewareFunction = Middleware

// 参数配置类型
export type ParameterConfig = RequiredKeys<ParameterObject, 'name' | 'in'>

// Swagger 路由器配置类型
export interface SwaggerRouterConfig {
  /** Swagger JSON 文档端点 */
  swaggerJsonEndpoint?: string
  /** Swagger HTML UI 端点 */
  swaggerHtmlEndpoint?: string
  /** Swagger UI 配置 */
  swaggerUIConfig?: SwaggerUIConfig
  /** 是否验证响应 */
  validateResponse?: boolean
  /** 是否验证请求 */
  validateRequest?: boolean
  /** OpenAPI 规范配置 */
  spec?: Partial<OpenAPIObjectConfig>
  /** 注册回调函数 */
  registryOpenApi?: (registry: OpenAPIRegistry, zod: ZodTypeAny) => void
  /** 全局 Schema 定义 */
  globalSchemas?: Record<string, ZodTypeAny>
}

export interface SwaggerUIConfig extends Partial<KoaSwaggerUiOptions> {
  routePrefix?: string
  swaggerOptions?: {
    url?: string
    swaggerCdnUrl?: string
    supportedSubmitMethods?: string[]
    docExpansion?: 'none' | 'list' | 'full'
    persistAuthorization?: boolean
    defaultModelRendering?: 'example' | 'model' | 'schema'
    [key: string]: any
  }
  hideTopbar?: boolean
  title?: string
  swaggerVersion?: string
}

// 元数据键类型
export type MetadataKey
  = | 'swagger:controller'
    | 'swagger:routeConfig'
    | 'swagger:request'
    | 'swagger:body'
    | 'swagger:responses'
    | 'swagger:responses_all'
    | 'swagger:header'
    | 'swagger:header_all'
    | 'swagger:middlewares'
    | 'swagger:summary'
    | 'swagger:description'
    | 'swagger:tags'
    | 'swagger:tags_all'
    | 'swagger:query'
    | 'swagger:path'
    | 'swagger:query_all'

// 装饰器类型
export type DecoratorType = 'class' | 'method' | 'property' | 'parameter'

// 装饰器工厂类型
export type DecoratorFactory<T extends DecoratorType> = T extends 'class'
  ? ClassDecorator
  : T extends 'method'
    ? MethodDecorator
    : T extends 'property'
      ? PropertyDecorator
      : T extends 'parameter'
        ? ParameterDecorator
        : never

// 装饰器处理函数类型
export type DecoratorHandler = (
  target: any,
  propertyKey?: string | symbol,
  descriptor?: PropertyDescriptor | number
) => any

// 装饰器选项类型
export interface DecoratorOptions {
  /** 装饰器类型 */
  type: DecoratorType
  /** 装饰器元数据 */
  metadata?: Record<string, any>
  /** 装饰器处理函数 */
  handler?: DecoratorHandler
  /** 是否启用日志记录 */
  enableLogging?: boolean
  /** 自定义元数据键前缀 */
  metadataKeyPrefix?: string
  /** 是否禁用内置元数据存储 */
  disableBuiltinMetadata?: boolean
}

// 缓存统计类型
export interface CacheStats {
  /** 缓存大小 */
  size: number
  /** 是否启用缓存 */
  enabled: boolean
}

// 合并策略类型
export type MergeStrategy = 'replace' | 'append' | 'merge'

// 设置元数据函数类型
export type SetMetadata = <T>(
  metadataKey: string,
  metadataValue: T,
  target: any,
  propertyKey?: string | symbol
) => void

// 获取元数据函数类型
export type GetMetadata = <T>(
  metadataKey: string,
  target: any,
  propertyKey?: string | symbol
) => T | undefined

// 类信息类型
export interface ClassInfo {
  /** 类名 */
  name: string
  /** 方法列表 */
  methods: MethodInfo[]
  /** 类级别元数据 */
  metadata: Record<string, any>
}

// 方法信息类型
export interface MethodInfo {
  /** 方法名 */
  name: string
  /** 方法级别元数据 */
  metadata: Record<string, any>
}

// 装饰器系统配置类型
export interface DecoratorSystemConfig {
  /** 是否启用日志 */
  enableLogging: boolean
  /** 是否启用缓存 */
  enableCache: boolean
  /** 元数据键前缀 */
  metadataKeyPrefix: string
}

// 验证选项类型
export interface ValidationOptions {
  /** 是否验证请求 */
  validateRequest: boolean
  /** 是否验证响应 */
  validateResponse: boolean
  /** 是否抛出验证错误 */
  throwOnError: boolean
}

// 错误处理选项类型
export interface ErrorHandlingOptions {
  /** 是否记录错误日志 */
  logErrors: boolean
  /** 是否包含堆栈跟踪 */
  includeStack: boolean
  /** 是否包含详细信息 */
  includeDetails: boolean
}

// Swagger 构建选项类型
export interface SwaggerBuildOptions {
  /** 路由前缀 */
  prefix?: string
  /** 全局 Schema */
  globalSchemas?: Record<string, ZodTypeAny>
  /** OpenAPI 规范配置 */
  spec?: Partial<OpenAPIObject>
  /** 是否启用调试模式 */
  debug?: boolean
  /** 是否启用缓存 */
  enableCache?: boolean
}

// 导出文件选项类型
export interface DumpOptions {
  /** 导出目录 */
  dir?: string
  /** 文件名 */
  fileName?: string
}

// 装饰器元数据映射类型
export interface DecoratorMetadataMap {
  /** 控制器元数据 */
  [METADATA_KEYS.CONTROLLER]: ControllerConfig
  /** 路由配置元数据 */
  [METADATA_KEYS.ROUTE_CONFIG]: StrongRouteConfig
  /** 请求体元数据 */
  [METADATA_KEYS.BODY]: ZodObject<any>
  /** 响应元数据 */
  [METADATA_KEYS.RESPONSES]: ResponsesConfig
  /** 请求头元数据 */
  [METADATA_KEYS.HEADER]: HeaderConfig
  /** 中间件元数据 */
  [METADATA_KEYS.MIDDLEWARES]: MiddlewareFunction[]
  /** 摘要元数据 */
  [METADATA_KEYS.SUMMARY]: string
  /** 描述元数据 */
  [METADATA_KEYS.DESCRIPTION]: string
  /** 标签元数据 */
  [METADATA_KEYS.TAGS]: string[]
  /** 查询参数元数据 */
  [METADATA_KEYS.QUERY]: ParameterConfig
  /** 路径参数元数据 */
  [METADATA_KEYS.PATH]: ParameterConfig
}

// 元数据键常量
export const METADATA_KEYS = {
  /** 控制器元数据键 */
  CONTROLLER: 'swagger:controller',
  /** 路由配置元数据键 */
  ROUTE_CONFIG: 'swagger:routeConfig',
  /** 请求元数据键 */
  REQUEST: 'swagger:request',
  /** 请求体元数据键 */
  BODY: 'swagger:body',
  /** 响应元数据键 */
  RESPONSES: 'swagger:responses',
  /** 请求头元数据键 */
  HEADER: 'swagger:header',
  /** 查询参数元数据键 */
  QUERY: 'swagger:query',
  /** 路径参数元数据键 */
  PATH: 'swagger:path',
  /** 中间件元数据键 */
  MIDDLEWARES: 'swagger:middlewares',
  /** 摘要元数据键 */
  SUMMARY: 'swagger:summary',
  /** 描述元数据键 */
  DESCRIPTION: 'swagger:description',
  /** 标签元数据键 */
  TAGS: 'swagger:tags',
  /** 类级别标签元数据键 */
  TAGS_ALL: 'swagger:tagsAll',
  /** 类级别请求头元数据键 */
  HEADER_ALL: 'swagger:headerAll',
  /** 类级别查询参数元数据键 */
  QUERY_ALL: 'swagger:queryAll',
  /** 类级别响应元数据键 */
  RESPONSES_ALL: 'swagger:responsesAll',
} as const

/**
 * 类型守卫工具函数
 *
 * 提供核心的运行时类型检查，专注于项目实际需求：
 * - Zod 类型检查（装饰器验证）
 * - HTTP 方法验证（路由配置）
 * - 基础类型安全检查
 */

/**
 * 检查值是否为 Zod 对象 Schema
 *
 * 用于装饰器中验证传入的 Schema 参数
 *
 * @param value - 要检查的值
 * @returns 是否为 Zod 对象 Schema
 *
 * @example
 * ```typescript
 * import { isZodObject } from './types'
 * import { z } from 'zod'
 *
 * const schema = z.object({ name: z.string() })
 * if (isZodObject(schema)) {
 *   // schema 是有效的 Zod 对象
 * }
 * ```
 */
export function isZodObject(value: any): value is ZodObject<any> {
  return value
    && typeof value === 'object'
    && (
      // Zod v4 structure
      ('_zod' in value && value._zod?.traits?.has('ZodObject'))
      // Zod v4 alternative check
      || ('_def' in value && value._def?.type === 'object')
      // Zod v3 structure (fallback)
      || ('_def' in value && value._def?.typeName === 'ZodObject')
    )
    && 'shape' in value
    && typeof value.shape === 'object'
}

/**
 * 检查值是否为任意 Zod 类型
 *
 * 更宽泛的 Zod 类型检查，支持所有 Zod Schema
 *
 * @param value - 要检查的值
 * @returns 是否为 Zod 类型
 *
 * @example
 * ```typescript
 * import { isZodType } from './types'
 * import { z } from 'zod'
 *
 * const stringSchema = z.string()
 * const objectSchema = z.object({ id: z.number() })
 *
 * if (isZodType(stringSchema)) {
 *   // 任何 Zod 类型都会返回 true
 * }
 * ```
 */
export function isZodType(value: any): value is ZodTypeAny {
  return value
    && typeof value === 'object'
    && (
      // Zod v4 structure
      ('_zod' in value && value._zod?.traits && value._zod.traits instanceof Set)
      // Zod v4 alternative check
      || ('_def' in value && typeof value._def === 'object' && 'type' in value._def)
      // Zod v3 structure (fallback)
      || ('_def' in value && typeof value._def === 'object' && 'typeName' in value._def && typeof value._def.typeName === 'string' && value._def.typeName.startsWith('Zod'))
    )
}

/**
 * 检查值是否为有效的 HTTP 方法
 *
 * @param value - 要检查的值
 * @returns 是否为 HTTP 方法
 *
 * @example
 * ```typescript
 * import { isHttpMethod } from './types'
 *
 * if (isHttpMethod('GET')) {
 *   // 有效的 HTTP 方法
 * }
 *
 * if (isHttpMethod('invalid')) {
 *   // 无效的方法，返回 false
 * }
 * ```
 */
export function isHttpMethod(value: any): value is HttpMethod {
  return typeof value === 'string'
    && ['get', 'post', 'put', 'delete', 'patch', 'options', 'head'].includes(value.toLowerCase())
}

/**
 * 检查值是否为 OpenAPI Schema 对象
 *
 * @param value - 要检查的值
 * @returns 是否为 Schema 对象
 *
 * @example
 * ```typescript
 * import { isSchemaObject } from './types'
 *
 * const schema = {
 *   type: 'object',
 *   properties: {
 *     name: { type: 'string' }
 *   }
 * }
 *
 * if (isSchemaObject(schema)) {
 *   // 有效的 OpenAPI Schema 对象
 * }
 * ```
 */
export function isSchemaObject(value: any): value is SchemaObject {
  if (!value || typeof value !== 'object') {
    return false
  }

  // 确保不是 Zod 类型
  if (isZodType(value)) {
    return false
  }

  // 确保不是响应配置对象（不应该有数字键）
  if (Object.keys(value).some((key) => /^\d{3}$/.test(key))) {
    return false
  }

  // Schema 对象通常有 type、properties 或 $ref
  return 'type' in value || 'properties' in value || '$ref' in value
}

// 工具类型
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

export type RequiredKeys<T, K extends keyof T> = T & Required<Pick<T, K>>

export type OptionalKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

// 导出所有类型
export type {
  Context,
  Middleware,
  OpenAPIObject,
  OpenAPIObjectConfig,
  OpenAPIRegistry,
  ParameterObject,
  ReferenceObject,
  ResponseObject,
  RouterOptions,
  ZodObject,
  ZodTypeAny,
}
