/**
 * 配置验证 Schema
 * 使用 Zod 定义配置验证 Schema，确保配置的类型安全
 */

import { z } from 'zod'
import { METADATA_KEYS } from '../types'

// HTTP 方法 Schema
export const httpMethodSchema = z.enum([
  'get',
  'post',
  'put',
  'delete',
  'patch',
  'options',
  'head',
])

// 控制器配置 Schema
export const controllerConfigSchema = z.object({
  tags: z.array(z.string()).optional(),
  paths: z.object({
    parameters: z.array(z.any()).optional(),
  }).optional(),
  components: z.record(z.string(), z.any()).optional(),
}).strict().optional()

// 路由配置 Schema
export const routeConfigSchema = z.object({
  method: httpMethodSchema,
  path: z.string().startsWith('/'),
  operationId: z.string().optional(),
  summary: z.string().optional(),
  description: z.string().optional(),
  tags: z.array(z.string()).optional(),
  parameters: z.record(z.string(), z.any()).optional(),
  request: z.object({
    body: z.object({
      content: z.record(z.string(), z.any()),
      required: z.boolean().optional(),
    }).optional(),
  }).optional(),
  responses: z.record(z.string(), z.any()).optional(),
  deprecated: z.boolean().optional(),
  security: z.array(z.record(z.string(), z.array(z.string()))).optional(),
  servers: z.array(
    z.object({
      url: z.string(),
      description: z.string().optional(),
    }),
  ).optional(),
  externalDocs: z.object({
    url: z.string(),
    description: z.string().optional(),
  }).optional(),
}).strict()

// 响应配置 Schema
export const responseConfigSchema = z.object({
  description: z.string(),
  schema: z.any().optional(),
  headers: z.record(z.string(), z.any()).optional(),
}).strict()

// 响应配置映射 Schema
export const responsesConfigSchema = z.record(
  z.string().regex(/^\d{3}$/),
  responseConfigSchema,
)

// Swagger UI 配置 Schema
export const swaggerUIConfigSchema = z.object({
  routePrefix: z.string().optional(),
  title: z.string().optional(),
  hideTopbar: z.boolean().optional(),
  swaggerOptions: z.object({
    docExpansion: z.enum(['list', 'full', 'none']).optional(),
    persistAuthorization: z.boolean().optional(),
    supportedSubmitMethods: z.array(httpMethodSchema).optional(),
    defaultModelRendering: z.enum(['example', 'model', 'schema']).optional(),
    url: z.string().optional(),
  }).optional(),
}).strict().optional()

// Swagger 路由器配置 Schema
export const swaggerRouterConfigSchema = z.object({
  swaggerJsonEndpoint: z.string().default('/swagger.json'),
  swaggerHtmlEndpoint: z.string().default('/swagger'),
  swaggerUIConfig: swaggerUIConfigSchema,
  validateResponse: z.boolean().default(false),
  validateRequest: z.boolean().default(true),
  spec: z.record(z.string(), z.any()).optional(),
  registryOpenApi: z.any().optional(),
  globalSchemas: z.record(z.string(), z.any()).optional(),
}).strict().partial()

// 导出文件选项 Schema
export const dumpOptionsSchema = z.object({
  dir: z.string().optional(),
  fileName: z.string().optional(),
}).strict().optional()

// 元数据验证函数
export function validateMetadata(key: string, value: any): boolean {
  switch (key) {
    case METADATA_KEYS.CONTROLLER:
      return controllerConfigSchema.safeParse(value).success

    case METADATA_KEYS.ROUTE_CONFIG:
      return routeConfigSchema.safeParse(value).success

    case METADATA_KEYS.RESPONSES:
      return responsesConfigSchema.safeParse(value).success

    default:
      return true
  }
}

// 配置验证函数
export function validateSwaggerRouterConfig(config: any) {
  const result = swaggerRouterConfigSchema.safeParse(config)
  if (!result.success) {
    return {
      valid: false,
      errors: result.error.issues.map((err) => `${err.path.join('.')}: ${err.message}`),
      details: result.error.issues,
    }
  }
  return { valid: true, data: result.data }
}

// 路由配置验证函数
export function validateRouteConfig(config: any) {
  const result = routeConfigSchema.safeParse(config)
  if (!result.success) {
    return {
      valid: false,
      errors: result.error.issues.map((err) => `${err.path.join('.')}: ${err.message}`),
      details: result.error.issues,
    }
  }
  return { valid: true, data: result.data }
}

// 控制器配置验证函数
export function validateControllerConfig(config: any) {
  const result = controllerConfigSchema.safeParse(config)
  if (!result.success) {
    return {
      valid: false,
      errors: result.error.issues.map((err) => `${err.path.join('.')}: ${err.message}`),
      details: result.error.issues,
    }
  }
  return { valid: true, data: result.data }
}

// 响应配置验证函数
export function validateResponsesConfig(config: any) {
  const result = responsesConfigSchema.safeParse(config)
  if (!result.success) {
    return {
      valid: false,
      errors: result.error.issues.map((err) => `${err.path.join('.')}: ${err.message}`),
      details: result.error.issues,
    }
  }
  return { valid: true, data: result.data }
}
