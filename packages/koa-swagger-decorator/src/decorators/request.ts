import type { HttpMethod } from '../types'
/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025-07-16 16:46:39
 * @LastEditTime: 2025-07-21 16:27:37
 * @LastEditors: shaojun
 * @Description: Request 装饰器实现 - 兼容旧系统的 request 装饰器
 */
import { METADATA_KEYS } from '../types'

/**
 * @Request 装饰器 - 定义 HTTP 方法和路径
 * @param method HTTP 方法
 * @param path 路径
 * @returns MethodDecorator
 * @example
 * @Request('post', '/user/login')
 * @Request('get', '/user/:id')
 */
export const Request = (method: HttpMethod, path: string): MethodDecorator => {
  if (!method || typeof method !== 'string') {
    throw new Error('Request method must be a non-empty string')
  }
  if (!path || typeof path !== 'string') {
    throw new Error('Request path must be a non-empty string')
  }

  // 标准化方法名为小写
  const normalizedMethod = method.toLowerCase() as HttpMethod

  return (target: any, propertyKey: string | symbol, _descriptor: PropertyDescriptor) => {
    const operationId = String(propertyKey)

    // 存储请求元数据
    const metadata = {
      method: normalizedMethod,
      path,
      operationId,
    }

    Reflect.defineMetadata(METADATA_KEYS.REQUEST, metadata, target, propertyKey)

    // 为了兼容性，同时设置旧的元数据键
    Reflect.defineMetadata('request', metadata, target, propertyKey)
  }
}

/**
 * 获取请求元数据
 * @param target 目标对象
 * @param methodName 方法名
 * @returns 请求元数据或 undefined
 */
export function getRequestMetadata(target: any, methodName: string): { method: HttpMethod, path: string, operationId: string } | undefined {
  return Reflect.getMetadata(METADATA_KEYS.REQUEST, target, methodName)
    || Reflect.getMetadata('request', target, methodName)
}

/**
 * 检查方法是否有请求装饰器
 * @param target 目标对象
 * @param methodName 方法名
 * @returns 是否有请求装饰器
 */
export function hasRequestDecorator(target: any, methodName: string): boolean {
  return Reflect.hasMetadata(METADATA_KEYS.REQUEST, target, methodName)
    || Reflect.hasMetadata('request', target, methodName)
}
