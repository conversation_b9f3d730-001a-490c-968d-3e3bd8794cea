/**
 * @fileoverview 元数据管理器 - 高性能元数据缓存和管理系统
 *
 * 提供优化的元数据存储和读取机制，包括：
 * - 多层缓存策略
 * - 智能缓存失效
 * - 批量操作支持
 * - 性能监控
 * - 内存管理
 *
 * ### 性能特性：
 * - **L1 缓存**: 基于 WeakMap 的对象级缓存
 * - **L2 缓存**: 基于 Map 的全局缓存
 * - **智能预取**: 预测性元数据加载
 * - **批量处理**: 减少反射操作次数
 * - **内存优化**: 自动清理无效缓存
 *
 * <AUTHOR>
 * @since 2025-07-16
 */

import { MetadataError } from '../errors'
// import { METADATA_KEYS } from '../types'
import 'reflect-metadata'

/**
 * 缓存统计信息接口
 */
interface CacheStats {
  /** 总请求次数 */
  totalRequests: number
  /** 缓存命中次数 */
  cacheHits: number
  /** 缓存未命中次数 */
  cacheMisses: number
  /** L1 缓存命中次数 */
  l1Hits: number
  /** L2 缓存命中次数 */
  l2Hits: number
  /** 缓存大小 */
  cacheSize: number
  /** 最后重置时间 */
  lastReset: number
}

/**
 * 元数据缓存项接口
 */
interface CacheEntry<T = any> {
  /** 缓存的值 */
  value: T
  /** 创建时间戳 */
  timestamp: number
  /** 访问次数 */
  accessCount: number
  /** 最后访问时间 */
  lastAccess: number
  /** 是否为热点数据 */
  isHot: boolean
}

/**
 * 批量操作接口
 */
interface BatchOperation {
  /** 操作类型 */
  type: 'get' | 'set' | 'delete'
  /** 元数据键 */
  key: string
  /** 目标对象 */
  target: any
  /** 属性键 */
  propertyKey?: string | symbol
  /** 值（仅用于 set 操作） */
  value?: any
}

/**
 * 高性能元数据管理器
 *
 * 使用多层缓存策略和智能优化算法，提供高性能的元数据存储和访问
 *
 * ### 缓存架构：
 * ```
 * ┌─────────────────┐
 * │   L1 Cache      │ ← WeakMap，对象级缓存
 * │   (WeakMap)     │
 * └─────────────────┘
 *          ↓
 * ┌─────────────────┐
 * │   L2 Cache      │ ← Map，全局缓存
 * │   (Map)         │
 * └─────────────────┘
 *          ↓
 * ┌─────────────────┐
 * │   Reflect API   │ ← 原始反射操作
 * └─────────────────┘
 * ```
 *
 * @example
 * ```typescript
 * const manager = MetadataManager.getInstance()
 *
 * // 设置元数据
 * manager.setMetadata('key', value, target, propertyKey)
 *
 * // 获取元数据（自动缓存）
 * const value = manager.getMetadata('key', target, propertyKey)
 *
 * // 批量操作
 * manager.batchOperation([
 *   { type: 'set', key: 'key1', target: obj1, value: value1 },
 *   { type: 'set', key: 'key2', target: obj2, value: value2 }
 * ])
 *
 * // 获取性能统计
 * const stats = manager.getCacheStats()
 * ```
 */
export class MetadataManager {
  private static instance: MetadataManager

  /** L1 缓存 - 基于 WeakMap 的对象级缓存 */
  private readonly l1Cache = new WeakMap<any, Map<string, CacheEntry>>()

  /** L2 缓存 - 基于 Map 的全局缓存 */
  private readonly l2Cache = new Map<string, CacheEntry>()

  /** 缓存统计信息 */
  private readonly stats: CacheStats = {
    totalRequests: 0,
    cacheHits: 0,
    cacheMisses: 0,
    l1Hits: 0,
    l2Hits: 0,
    cacheSize: 0,
    lastReset: Date.now(),
  }

  /** 缓存配置 */
  private readonly config = {
    /** L2 缓存最大大小 */
    maxL2CacheSize: 10000,
    /** 缓存项最大生存时间（毫秒） */
    maxAge: 5 * 60 * 1000, // 5分钟
    /** 热点数据访问阈值 */
    hotDataThreshold: 10,
    /** 清理间隔（毫秒） */
    cleanupInterval: 60 * 1000, // 1分钟
  }

  /** 清理定时器 */
  private cleanupTimer?: NodeJS.Timeout

  private constructor() {
    this.startCleanupTimer()
  }

  /**
   * 获取单例实例
   *
   * @returns MetadataManager 实例
   */
  public static getInstance(): MetadataManager {
    if (!MetadataManager.instance) {
      MetadataManager.instance = new MetadataManager()
    }
    return MetadataManager.instance
  }

  /**
   * 设置元数据
   *
   * 使用多层缓存策略存储元数据，同时更新缓存
   *
   * @param key - 元数据键
   * @param value - 元数据值
   * @param target - 目标对象
   * @param propertyKey - 属性键（可选）
   * @throws {MetadataError} 当操作失败时抛出错误
   */
  public setMetadata<T = any>(
    key: string,
    value: T,
    target: any,
    propertyKey?: string | symbol,
  ): void {
    try {
      // 执行原始的元数据设置
      if (propertyKey !== undefined) {
        Reflect.defineMetadata(key, value, target, propertyKey)
      } else {
        Reflect.defineMetadata(key, value, target)
      }

      // 更新缓存
      this.updateCache(key, value, target, propertyKey)
    } catch (error) {
      throw new MetadataError(
        `Failed to set metadata: ${error instanceof Error ? error.message : 'Unknown error'}`,
        {
          key,
          target: this.getTargetName(target),
          propertyKey: propertyKey ? String(propertyKey) : undefined,
        },
        error instanceof Error ? error : undefined,
      )
    }
  }

  /**
   * 获取元数据
   *
   * 使用多层缓存策略获取元数据，自动处理缓存未命中
   *
   * @param key - 元数据键
   * @param target - 目标对象
   * @param propertyKey - 属性键（可选）
   * @returns 元数据值，如果不存在则返回 undefined
   * @throws {MetadataError} 当操作失败时抛出错误
   */
  public getMetadata<T = any>(
    key: string,
    target: any,
    propertyKey?: string | symbol,
  ): T | undefined {
    this.stats.totalRequests++

    try {
      // 尝试从 L1 缓存获取
      const l1Result = this.getFromL1Cache<T>(key, target, propertyKey)
      if (l1Result !== undefined) {
        this.stats.cacheHits++
        this.stats.l1Hits++
        this.updateAccessStats(key, target, propertyKey, 'l1')
        return l1Result
      }

      // 尝试从 L2 缓存获取
      const l2Result = this.getFromL2Cache<T>(key, target, propertyKey)
      if (l2Result !== undefined) {
        this.stats.cacheHits++
        this.stats.l2Hits++
        this.updateAccessStats(key, target, propertyKey, 'l2')
        // 将热点数据提升到 L1 缓存
        this.promoteToL1Cache(key, l2Result, target, propertyKey)
        return l2Result
      }

      // 缓存未命中，从 Reflect API 获取
      this.stats.cacheMisses++
      let value: T | undefined

      if (propertyKey !== undefined) {
        value = Reflect.getMetadata(key, target, propertyKey)
      } else {
        value = Reflect.getMetadata(key, target)
      }

      // 如果找到值，则缓存它
      if (value !== undefined) {
        this.updateCache(key, value, target, propertyKey)
      }

      return value
    } catch (error) {
      throw new MetadataError(
        `Failed to get metadata: ${error instanceof Error ? error.message : 'Unknown error'}`,
        {
          key,
          target: this.getTargetName(target),
          propertyKey: propertyKey ? String(propertyKey) : undefined,
        },
        error instanceof Error ? error : undefined,
      )
    }
  }

  /**
   * 检查元数据是否存在
   *
   * @param key - 元数据键
   * @param target - 目标对象
   * @param propertyKey - 属性键（可选）
   * @returns 是否存在元数据
   */
  public hasMetadata(
    key: string,
    target: any,
    propertyKey?: string | symbol,
  ): boolean {
    // 先检查缓存
    if (this.getFromL1Cache(key, target, propertyKey) !== undefined) {
      return true
    }

    if (this.getFromL2Cache(key, target, propertyKey) !== undefined) {
      return true
    }

    // 最后检查 Reflect API
    if (propertyKey !== undefined) {
      return Reflect.hasMetadata(key, target, propertyKey)
    } else {
      return Reflect.hasMetadata(key, target)
    }
  }

  /**
   * 删除元数据
   *
   * @param key - 元数据键
   * @param target - 目标对象
   * @param propertyKey - 属性键（可选）
   */
  public deleteMetadata(
    key: string,
    target: any,
    propertyKey?: string | symbol,
  ): void {
    try {
      // 删除原始元数据
      if (propertyKey !== undefined) {
        Reflect.deleteMetadata(key, target, propertyKey)
      } else {
        Reflect.deleteMetadata(key, target)
      }

      // 清除缓存
      this.clearFromCache(key, target, propertyKey)
    } catch (error) {
      throw new MetadataError(
        `Failed to delete metadata: ${error instanceof Error ? error.message : 'Unknown error'}`,
        {
          key,
          target: this.getTargetName(target),
          propertyKey: propertyKey ? String(propertyKey) : undefined,
        },
        error instanceof Error ? error : undefined,
      )
    }
  }

  /**
   * 批量操作
   *
   * 执行多个元数据操作，减少反射调用次数
   *
   * @param operations - 操作数组
   * @returns 操作结果数组
   */
  public batchOperation(operations: BatchOperation[]): (any | undefined)[] {
    const results: (any | undefined)[] = []

    for (const operation of operations) {
      try {
        switch (operation.type) {
          case 'get':
            results.push(this.getMetadata(operation.key, operation.target, operation.propertyKey))
            break
          case 'set':
            this.setMetadata(operation.key, operation.value, operation.target, operation.propertyKey)
            results.push(undefined)
            break
          case 'delete':
            this.deleteMetadata(operation.key, operation.target, operation.propertyKey)
            results.push(undefined)
            break
          default:
            throw new Error(`Unknown operation type: ${(operation as any).type}`)
        }
      } catch (error) {
        results.push(error)
      }
    }

    return results
  }

  /**
   * 获取缓存统计信息
   *
   * @returns 缓存统计信息
   */
  public getCacheStats(): CacheStats {
    return {
      ...this.stats,
      cacheSize: this.l2Cache.size,
    }
  }

  /**
   * 重置缓存统计
   */
  public resetStats(): void {
    this.stats.totalRequests = 0
    this.stats.cacheHits = 0
    this.stats.cacheMisses = 0
    this.stats.l1Hits = 0
    this.stats.l2Hits = 0
    this.stats.lastReset = Date.now()
  }

  /**
   * 清空所有缓存
   */
  public clearCache(): void {
    this.l2Cache.clear()
    this.stats.cacheSize = 0
  }

  /**
   * 获取缓存命中率
   *
   * @returns 缓存命中率（0-1）
   */
  public getCacheHitRate(): number {
    if (this.stats.totalRequests === 0) {
      return 0
    }
    return this.stats.cacheHits / this.stats.totalRequests
  }

  // 私有方法

  /**
   * 从 L1 缓存获取数据
   */
  private getFromL1Cache<T>(
    key: string,
    target: any,
    propertyKey?: string | symbol,
  ): T | undefined {
    const targetCache = this.l1Cache.get(target)
    if (!targetCache) {
      return undefined
    }

    const cacheKey = this.generateCacheKey(key, propertyKey)
    const entry = targetCache.get(cacheKey)

    if (entry && this.isValidCacheEntry(entry)) {
      return entry.value as T
    }

    return undefined
  }

  /**
   * 从 L2 缓存获取数据
   */
  private getFromL2Cache<T>(
    key: string,
    target: any,
    propertyKey?: string | symbol,
  ): T | undefined {
    const cacheKey = this.generateGlobalCacheKey(key, target, propertyKey)
    const entry = this.l2Cache.get(cacheKey)

    if (entry && this.isValidCacheEntry(entry)) {
      return entry.value as T
    }

    return undefined
  }

  /**
   * 更新缓存
   */
  private updateCache<T>(
    key: string,
    value: T,
    target: any,
    propertyKey?: string | symbol,
  ): void {
    const now = Date.now()
    const entry: CacheEntry<T> = {
      value,
      timestamp: now,
      accessCount: 1,
      lastAccess: now,
      isHot: false,
    }

    // 更新 L1 缓存
    this.updateL1Cache(key, entry, target, propertyKey)

    // 更新 L2 缓存
    this.updateL2Cache(key, entry, target, propertyKey)
  }

  /**
   * 更新 L1 缓存
   */
  private updateL1Cache<T>(
    key: string,
    entry: CacheEntry<T>,
    target: any,
    propertyKey?: string | symbol,
  ): void {
    let targetCache = this.l1Cache.get(target)
    if (!targetCache) {
      targetCache = new Map()
      this.l1Cache.set(target, targetCache)
    }

    const cacheKey = this.generateCacheKey(key, propertyKey)
    targetCache.set(cacheKey, entry)
  }

  /**
   * 更新 L2 缓存
   */
  private updateL2Cache<T>(
    key: string,
    entry: CacheEntry<T>,
    target: any,
    propertyKey?: string | symbol,
  ): void {
    // 检查缓存大小限制
    if (this.l2Cache.size >= this.config.maxL2CacheSize) {
      this.evictLRUEntries()
    }

    const cacheKey = this.generateGlobalCacheKey(key, target, propertyKey)
    this.l2Cache.set(cacheKey, entry)
  }

  /**
   * 将数据提升到 L1 缓存
   */
  private promoteToL1Cache<T>(
    key: string,
    value: T,
    target: any,
    propertyKey?: string | symbol,
  ): void {
    const entry: CacheEntry<T> = {
      value,
      timestamp: Date.now(),
      accessCount: 1,
      lastAccess: Date.now(),
      isHot: true,
    }

    this.updateL1Cache(key, entry, target, propertyKey)
  }

  /**
   * 从缓存中清除数据
   */
  private clearFromCache(
    key: string,
    target: any,
    propertyKey?: string | symbol,
  ): void {
    // 清除 L1 缓存
    const targetCache = this.l1Cache.get(target)
    if (targetCache) {
      const cacheKey = this.generateCacheKey(key, propertyKey)
      targetCache.delete(cacheKey)
    }

    // 清除 L2 缓存
    const globalCacheKey = this.generateGlobalCacheKey(key, target, propertyKey)
    this.l2Cache.delete(globalCacheKey)
  }

  /**
   * 更新访问统计
   */
  private updateAccessStats(
    key: string,
    target: any,
    propertyKey: string | symbol | undefined,
    cacheLevel: 'l1' | 'l2',
  ): void {
    const cacheKey = cacheLevel === 'l1'
      ? this.generateCacheKey(key, propertyKey)
      : this.generateGlobalCacheKey(key, target, propertyKey)

    const cache = cacheLevel === 'l1'
      ? this.l1Cache.get(target)
      : this.l2Cache

    if (cache) {
      const entry = cacheLevel === 'l1'
        ? (cache as Map<string, CacheEntry>).get(cacheKey)
        : (cache as Map<string, CacheEntry>).get(cacheKey)

      if (entry) {
        entry.accessCount++
        entry.lastAccess = Date.now()

        // 标记为热点数据
        if (entry.accessCount >= this.config.hotDataThreshold) {
          entry.isHot = true
        }
      }
    }
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(key: string, propertyKey?: string | symbol): string {
    return propertyKey ? `${key}:${String(propertyKey)}` : key
  }

  /**
   * 生成全局缓存键
   */
  private generateGlobalCacheKey(
    key: string,
    target: any,
    propertyKey?: string | symbol,
  ): string {
    const targetName = this.getTargetName(target)
    const propKey = propertyKey ? String(propertyKey) : ''
    return `${targetName}:${propKey}:${key}`
  }

  /**
   * 获取目标对象名称
   */
  private getTargetName(target: any): string {
    return target?.constructor?.name || target?.name || 'Unknown'
  }

  /**
   * 检查缓存项是否有效
   */
  private isValidCacheEntry(entry: CacheEntry): boolean {
    const now = Date.now()
    return (now - entry.timestamp) < this.config.maxAge
  }

  /**
   * 驱逐 LRU 缓存项
   */
  private evictLRUEntries(): void {
    const entries = Array.from(this.l2Cache.entries())

    // 按最后访问时间排序
    entries.sort((a, b) => a[1].lastAccess - b[1].lastAccess)

    // 删除最老的 20% 缓存项
    const evictCount = Math.floor(entries.length * 0.2)
    for (let i = 0; i < evictCount; i++) {
      this.l2Cache.delete(entries[i][0])
    }
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanupExpiredEntries()
    }, this.config.cleanupInterval)
  }

  /**
   * 清理过期缓存项
   */
  private cleanupExpiredEntries(): void {
    const now = Date.now()

    for (const [key, entry] of this.l2Cache.entries()) {
      if ((now - entry.timestamp) >= this.config.maxAge) {
        this.l2Cache.delete(key)
      }
    }
  }

  /**
   * 销毁管理器
   */
  public destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = undefined
    }

    this.clearCache()
    this.resetStats()
  }
}

// 导出单例实例
export const metadataManager = MetadataManager.getInstance()

// 导出便捷函数
export function getMetadata<T = any>(
  key: string,
  target: object,
  propertyKey?: string | symbol,
): T | undefined {
  return metadataManager.getMetadata<T>(key, target, propertyKey)
}
