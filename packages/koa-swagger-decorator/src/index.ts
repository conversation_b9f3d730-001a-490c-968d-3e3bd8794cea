import { extendZodWithOpenApi } from '@asteasolutions/zod-to-openapi'
import { z } from 'zod'
import { registry } from './registry'
import 'reflect-metadata'

// 扩展zod
extendZodWithOpenApi(z)

export declare type ArgsKey = 'query' | 'params' | 'body'

export declare type ParsedKeyArgs<K extends ArgsKey, T> = {
  [key in K]?: T
}
export declare type ParsedArgs<T> = ParsedKeyArgs<'query' | 'params' | 'body', T>

export { registry, z }

// ================================1
// 构建器系统
// ================================
export * from './builder'

// ================================
// 装饰器系统
// ================================
export * from './decorators'

// ================================
// 错误处理
// ================================
export * from './errors'

// ================================
// 全局配置
// ================================
export * from './global'

// ================================
// 路由系统
// ================================
export * from './router'

// ================================
// 核心类型系统
// ================================
export * from './types'

// ================================
// UI系统
// ================================
export * from './ui'

// ================================
// 工具函数
// ================================
export * from './utils'
