<!--
 * @Author: s<PERSON><PERSON>
 * @Date: 2025-07-21 15:53:49
 * @LastEditTime: 2025-07-21 15:58:35
 * @LastEditors: shaojun
 * @Description: Zod v4 升级任务列表和执行计划
-->

# Zod v4 升级任务列表

## 📋 升级概览

本文档记录了将 `@cfe-node/koa-swagger-decorator` 从 Zod v3 升级到 Zod v4 的详细任务列表和执行步骤。

### 🎯 升级目标
- 升级到 Zod v4.0.0+
- 升级 zod-to-openapi 到 v8.0.0+
- 保持 API 向后兼容性
- 利用 Zod v4 的性能和功能改进

### 📊 影响评估
- **高影响**: 错误处理、类型守卫、Schema 收集
- **中影响**: 装饰器实现、测试用例
- **低影响**: 示例代码、文档

---

## 🚀 阶段 1: 依赖升级和基础准备

### 1.1 依赖包升级
- [ ] **升级 Zod 核心包**
  ```bash
  pnpm update zod@^4.0.0
  ```
  - 当前版本: `3.22.4`
  - 目标版本: `4.0.0+`
  - 影响: 所有 Zod 相关功能

- [ ] **升级 zod-to-openapi**
  ```bash
  pnpm update @asteasolutions/zod-to-openapi@^8.0.0
  ```
  - 当前版本: `7.3.4`
  - 目标版本: `8.0.0+`
  - 影响: OpenAPI Schema 生成

- [ ] **检查 TypeScript 兼容性**
  - 确保 TypeScript 版本支持 Zod v4
  - 当前版本: `5.2.2` ✅

### 1.2 基础兼容性验证
- [ ] **运行基础构建测试**
  ```bash
  pnpm build
  ```
- [ ] **运行类型检查**
  ```bash
  pnpm tsc --noEmit
  ```
- [ ] **识别编译错误**
  - 记录所有 TypeScript 错误
  - 分类错误类型和影响范围

---

## 🔧 阶段 2: 核心代码适配

### 2.1 类型守卫函数更新
- [ ] **更新 `isZodObject` 函数**
  - 文件: `src/types/index.ts:542-549`
  - 问题: `._def` 移动到 `._zod.def`
  - 修改内容:
    ```typescript
    // 旧版本
    value._def?.typeName === 'ZodObject'

    // 新版本 (需要验证)
    value._zod?.def?.typeName === 'ZodObject'
    ```

- [ ] **更新其他类型检查函数**
  - 检查所有使用 `._def` 的地方
  - 更新为新的内部结构访问方式

### 2.2 错误处理系统重构
- [ ] **更新错误自定义 API**
  - 文件: `src/decorators/body.ts`, `src/decorators/query.ts` 等
  - 变更: `message` → `error` 参数
  - 影响: 所有装饰器的错误处理

- [ ] **适配新的错误格式**
  - 文件: `src/errors/index.ts`
  - 更新错误处理逻辑以适配 Zod v4 错误结构
  - 测试错误信息的正确性

### 2.3 Schema 收集器适配
- [ ] **更新 Schema 注册逻辑**
  - 文件: `src/builder/schema-collector.ts:389-404`
  - 适配新的 Zod 内部结构
  - 确保 OpenAPI Schema 生成正确

- [ ] **验证 extendZodWithOpenApi 兼容性**
  - 文件: `src/index.ts:7`, `src/old/index.ts:7`
  - 确保扩展方法在 Zod v4 中正常工作

---

## 🆕 阶段 3: API 现代化和优化

### 3.1 采用新的字符串验证器
- [ ] **替换字符串格式验证**
  - 查找所有 `z.string().email()` 用法
  - 替换为 `z.email()`
  - 同样处理: `uuid()`, `url()`, `cuid()` 等

- [ ] **更新测试用例中的验证器**
  - 文件: `tests/case/` 目录下的所有文件
  - 确保新验证器的行为一致

### 3.2 对象处理优化
- [ ] **评估 `.strict()` 和 `.passthrough()` 使用**
  - 考虑替换为 `z.strictObject()` 和 `z.looseObject()`
  - 保持向后兼容性

- [ ] **更新 `.merge()` 为 `.extend()`**
  - 查找所有 `.merge()` 使用
  - 评估是否需要替换为 `.extend()`

### 3.3 函数验证更新 (如果使用)
- [ ] **检查 `z.function()` 使用**
  - 搜索项目中的函数验证使用
  - 如果存在，更新为新的函数工厂 API

---

## 🧪 阶段 4: 测试和验证

### 4.1 单元测试更新
- [ ] **更新所有测试用例**
  - 文件: `tests/` 目录下所有 `.test.ts` 文件
  - 适配新的 API 和错误格式
  - 确保测试覆盖率不降低

- [ ] **添加 Zod v4 特性测试**
  - 测试新的字符串验证器
  - 测试性能改进
  - 测试错误处理改进

### 4.2 集成测试
- [ ] **运行完整测试套件**
  ```bash
  pnpm test
  ```
- [ ] **验证 OpenAPI 生成**
  - 确保生成的 OpenAPI 文档正确
  - 对比升级前后的差异

- [ ] **性能基准测试**
  - 测量解析性能改进
  - 记录性能提升数据

### 4.3 兼容性测试
- [ ] **测试向后兼容性**
  - 确保现有 API 仍然工作
  - 测试现有项目的集成

- [ ] **测试边缘情况**
  - 复杂 Schema 验证
  - 嵌套对象处理
  - 错误场景处理

---

## 📚 阶段 5: 文档和示例更新

### 5.1 文档更新
- [ ] **更新 README.md**
  - 更新 Zod 版本要求
  - 添加 v4 新功能说明
  - 更新安装指南

- [ ] **更新 API 文档**
  - 文件: `decorators.md`, `design.md`
  - 更新示例代码
  - 添加迁移指南

### 5.2 示例代码更新
- [ ] **更新测试用例示例**
  - 文件: `tests/case/` 目录
  - 使用新的 API 风格
  - 展示 v4 新功能

- [ ] **更新集成示例**
  - 检查 `packages/server/` 中的使用示例
  - 确保示例代码现代化

---

## 🚀 阶段 6: 发布准备

### 6.1 版本管理
- [ ] **更新版本号**
  - 决定是否为 major 版本升级
  - 更新 `package.json` 版本

- [ ] **准备 CHANGELOG**
  - 记录所有破坏性变更
  - 记录新功能和改进
  - 提供迁移指南

### 6.2 发布策略
- [ ] **创建 beta 版本**
  - 先发布 beta 版本进行测试
  - 收集用户反馈

- [ ] **准备回滚计划**
  - 保留 Zod v3 兼容分支
  - 准备快速回滚方案

---

## 📝 执行记录

### 完成的任务
- [x] 完成升级可行性分析
- [x] 创建详细任务列表

### 进行中的任务
- [ ] 待开始...

### 遇到的问题
- 记录升级过程中遇到的问题和解决方案

### 性能对比
- 升级前后的性能数据对比
