import type {
  ComponentTypeOf,
  OpenAPIRegistry,
} from '@asteasolutions/zod-to-openapi/dist/openapi-registry'
import type { ReferenceObject, SchemaObject } from 'openapi3-ts/oas30'
import type { ZodTypeAny } from 'zod'
// 注意：extendZodWithOpenApi 应该在应用启动时调用，不在这里导入
import { globalSchemas } from '../global'
import { isZodType } from '../types'
import { refLink } from '../utils'
import { logger } from '../utils/logger'

/**
 * OpenAPI Schema 类型定义
 */
interface OpenAPISchema {
  type: string
  format?: string
  items?: OpenAPISchema
  properties?: Record<string, OpenAPISchema>
  required?: string[]
  enum?: any[]
  nullable?: boolean
  description?: string
  example?: any
}

/**
 * Zod 类型名称枚举
 */
enum ZodTypeName {
  ZodString = 'ZodString',
  ZodNumber = 'ZodNumber',
  ZodBoolean = 'ZodBoolean',
  ZodDate = 'ZodDate',
  ZodArray = 'ZodArray',
  ZodObject = 'ZodObject',
  ZodOptional = 'ZodOptional',
  ZodNullable = 'ZodNullable',
  ZodEnum = 'ZodEnum',
  ZodNativeEnum = 'ZodNativeEnum',
  ZodRecord = 'ZodRecord',
  ZodUnion = 'ZodUnion',
  ZodIntersection = 'ZodIntersection',
  ZodLiteral = 'ZodLiteral',
  ZodBigInt = 'ZodBigInt',
  ZodUnknown = 'ZodUnknown',
  ZodAny = 'ZodAny',
}

/**
 * Zod 到 OpenAPI 转换器类
 * 负责将 Zod Schema 转换为 OpenAPI Schema 格式
 */
class ZodToOpenAPIConverter {
  private static readonly DEFAULT_SCHEMA: OpenAPISchema = { type: 'object' }

  /**
   * 获取 Zod Schema 的 def 对象，兼容 Zod v3 和 v4
   * @param zodSchema Zod Schema 对象
   * @returns def 对象或 null
   */
  private static getDef(zodSchema: any): any {
    // Zod v3: ._def
    if (zodSchema._def) {
      return zodSchema._def
    }

    // Zod v4: ._zod.def
    if (zodSchema._zod?.def) {
      return zodSchema._zod.def
    }

    // 🔧 全局 schemas 的特殊处理：直接访问 .def 属性
    if (zodSchema.def && typeof zodSchema.def === 'object') {
      return zodSchema.def
    }

    return null
  }

  /**
   * 转换 Zod Schema 为 OpenAPI Schema
   * @param zodSchema Zod Schema 对象
   * @returns OpenAPI Schema 对象
   */
  static convert(zodSchema: ZodTypeAny): OpenAPISchema {
    try {
      return this.convertInternal(zodSchema)
    } catch (error) {
      logger.warn(`转换 Zod Schema 时发生错误: ${error instanceof Error ? error.message : String(error)}`)
      return this.DEFAULT_SCHEMA
    }
  }

  private static convertInternal(zodSchema: ZodTypeAny): OpenAPISchema {
    const def = this.getDef(zodSchema as any)

    // 🔍 添加调试信息
    console.log(`🔍 [CONVERTER] def 对象:`, def)
    console.log(`🔍 [CONVERTER] def.typeName:`, def?.typeName)
    console.log(`🔍 [CONVERTER] def.type:`, def?.type)
    console.log(`🔍 [CONVERTER] def 的所有属性:`, def ? Object.keys(def) : 'null')

    // 🔧 特殊处理：如果没有 typeName 但有 type 和 shape，说明是全局 schema
    if (!def?.typeName && def?.type === 'object' && def?.shape) {
      console.log(`🔍 [CONVERTER] 检测到全局 schema，直接转换为 object schema`)
      return this.convertObject(def)
    }

    if (!def?.typeName) {
      logger.warn(`无法获取 Zod Schema 的 typeName，使用默认 schema`)
      return this.DEFAULT_SCHEMA
    }

    const typeName = def.typeName as ZodTypeName

    switch (typeName) {
      case ZodTypeName.ZodString:
        return this.convertString(def)

      case ZodTypeName.ZodNumber:
        return this.convertNumber(def)

      case ZodTypeName.ZodBoolean:
        return { type: 'boolean' }

      case ZodTypeName.ZodDate:
        return { type: 'string', format: 'date-time' }

      case ZodTypeName.ZodBigInt:
        return { type: 'integer', format: 'int64' }

      case ZodTypeName.ZodArray:
        return this.convertArray(def)

      case ZodTypeName.ZodObject:
        return this.convertObject(def)

      case ZodTypeName.ZodOptional:
        return this.convertInternal(def.innerType)

      case ZodTypeName.ZodNullable:
        return this.convertNullable(def)

      case ZodTypeName.ZodEnum:
        return this.convertEnum(def)

      case ZodTypeName.ZodNativeEnum:
        return this.convertNativeEnum(def)

      case ZodTypeName.ZodRecord:
        return this.convertRecord(def)

      case ZodTypeName.ZodUnion:
        return this.convertUnion(def)

      case ZodTypeName.ZodLiteral:
        return this.convertLiteral(def)

      case ZodTypeName.ZodUnknown:
        return { type: 'object', description: 'Unknown type' }

      case ZodTypeName.ZodAny:
        return { type: 'object', description: 'Any type' }

      default:
        logger.warn(`未支持的 Zod 类型: ${typeName}`)
        return this.DEFAULT_SCHEMA
    }
  }

  private static convertString(def: any): OpenAPISchema {
    const schema: OpenAPISchema = { type: 'string' }

    // 处理字符串格式
    if (def.checks) {
      for (const check of def.checks) {
        switch (check.kind) {
          case 'email':
            schema.format = 'email'
            break
          case 'url':
            schema.format = 'uri'
            break
          case 'uuid':
            schema.format = 'uuid'
            break
          case 'datetime':
            schema.format = 'date-time'
            break
        }
      }
    }

    return schema
  }

  private static convertNumber(def: any): OpenAPISchema {
    const schema: OpenAPISchema = { type: 'number' }

    // 检查是否为整数
    if (def.checks) {
      const hasIntCheck = def.checks.some((check: any) => check.kind === 'int')
      if (hasIntCheck) {
        schema.type = 'integer'
      }
    }

    return schema
  }

  private static convertArray(def: any): OpenAPISchema {
    return {
      type: 'array',
      items: this.convertInternal(def.type),
    }
  }

  private static convertObject(def: any): OpenAPISchema {
    const properties: Record<string, OpenAPISchema> = {}
    const required: string[] = []

    if (def.shape) {
      const shape = typeof def.shape === 'function' ? def.shape() : def.shape

      for (const [key, value] of Object.entries(shape)) {
        properties[key] = this.convertInternal(value as ZodTypeAny)

        // 检查是否为必填字段 - 适配 Zod v4
        const valueDef = this.getDef(value as any)

        if (valueDef && valueDef.typeName !== ZodTypeName.ZodOptional) {
          required.push(key)
        }
      }
    }

    const result: OpenAPISchema = {
      type: 'object',
      properties,
    }

    if (required.length > 0) {
      result.required = required
    }

    return result
  }

  private static convertNullable(def: any): OpenAPISchema {
    const innerSchema = this.convertInternal(def.innerType)
    return {
      ...innerSchema,
      nullable: true,
    }
  }

  private static convertEnum(def: any): OpenAPISchema {
    return {
      type: 'string',
      enum: def.values,
    }
  }

  private static convertNativeEnum(def: any): OpenAPISchema {
    return {
      type: 'string',
      enum: Object.values(def.values),
    }
  }

  private static convertRecord(def: any): OpenAPISchema {
    return {
      type: 'object',
      additionalProperties: def.valueType ? this.convertInternal(def.valueType) : { type: 'string' },
    } as any
  }

  private static convertUnion(def: any): OpenAPISchema {
    // 简化处理：返回第一个类型
    if (def.options && def.options.length > 0) {
      return this.convertInternal(def.options[0])
    }
    return this.DEFAULT_SCHEMA
  }

  private static convertLiteral(def: any): OpenAPISchema {
    const value = def.value
    const type = typeof value

    return {
      type: type === 'string' ? 'string' : type === 'number' ? 'number' : 'boolean',
      enum: [value],
    }
  }
}

// Schema 元数据接口
export interface SchemaMeta {
  refId: string
  zodSchema: ZodTypeAny | ComponentTypeOf<'schemas'> | SchemaObject | ReferenceObject
}

// 收集 schemas 的全局数组
export const CollectionSchemas: SchemaMeta[] = []

/**
 * 添加全局 schemas
 * @param globals 全局 schemas 对象
 */
export function addGlobalSchemas(globals?: Record<string, ZodTypeAny>) {
  // 处理内置的 schemas (按照旧代码的逻辑)
  Object.entries(globalSchemas).forEach(([key, element]) => {
    if (element && typeof element === 'object') {
      logger.info(`📋 收集内置 schema: ${key}`)
      collectSchemas(key, element) // 不强制覆盖
    }
  })

  // 处理外部传入的 schemas (可以覆盖内置的 schemas)
  if (globals) {
    logger.info(`🌐 开始处理 ${Object.keys(globals).length} 个外部全局 schema`)

    for (const [refId, zodSchema] of Object.entries(globals)) {
      if (!refId || !zodSchema) {
        continue
      }

      // 验证 schema 是否有 openapi 方法
      const hasOpenapiMethod = typeof (zodSchema as any).openapi === 'function'
      if (hasOpenapiMethod) {
        try {
          (zodSchema as any).openapi({}) // 测试调用
          logger.info(`✅ 外部 schema ${refId} 验证通过`)
        } catch (error) {
          logger.warn(`⚠️ 外部 schema ${refId} openapi 方法调用失败: ${error instanceof Error ? error.message : String(error)}`)
        }
      } else {
        logger.warn(`⚠️ 外部 schema ${refId} 缺少 openapi 方法`)
      }

      // 🔧 对于全局 schemas，强制使用 collectSchema 注册，不依赖 .openapi() 方法
      // 这是因为全局 schemas 可能在 extendZodWithOpenApi 调用之前就被创建了
      logger.info(`🔧 强制注册全局 schema: ${refId}`)
      collectSchemas(refId, zodSchema, true) // 强制添加覆盖
    }
  }
}

/**
 * 收集 schemas
 * @param refId Schema 引用 ID
 * @param zodSchema Zod Schema 对象
 * @param force 是否强制覆盖已存在的 Schema
 */
export function collectSchemas(
  refId: string,
  zodSchema: ZodTypeAny,
  force = false,
) {
  if (!refId) {
    return
  }

  const isExist = (id: string) =>
    CollectionSchemas.some((o: SchemaMeta) => o.refId === id)

  if (force && isExist(refId)) {
    const index = CollectionSchemas.findIndex((o: SchemaMeta) => o.refId === refId)
    CollectionSchemas.splice(index, 1)
  } else if (!force && isExist(refId)) {
    return
  }

  // 调试：记录收集的 schema 信息
  const hasOpenapiMethod = typeof (zodSchema as any).openapi === 'function'
  const hasOpenapiProperty = (zodSchema as any).openapi && typeof (zodSchema as any).openapi === 'object'
  const hasOpenapi = hasOpenapiMethod || hasOpenapiProperty
  logger.info(`📋 收集 schema ${refId}: hasOpenapi=${hasOpenapi}, force=${force}`)

  CollectionSchemas.push({ refId, zodSchema })
}

/**
 * 注册所有收集到的 schemas
 * @param registry OpenAPI 注册表
 */
export function registerSchemas(registry: OpenAPIRegistry) {
  if (!CollectionSchemas.length) {
    return
  }

  logger.info(`📋 正在注册 ${CollectionSchemas.length} 个 Schema...`)

  // 注册所有收集到的 schemas
  CollectionSchemas.forEach((schema) => {
    try {
      registry.register(schema.refId, schema.zodSchema as ZodTypeAny)
      logger.info(`✅ 已注册 Schema: ${schema.refId}`)
    } catch (error) {
      logger.error(`❌ 注册 Schema 失败: ${schema.refId}, 错误: ${error instanceof Error ? error.message : String(error)}`)
    }
  })
}

/**
 * 创建全局引用对象
 * @param globalKey 全局 Schema 键
 * @returns Schema 对象或引用对象
 */
export const globalRefObject = (
  globalKey: string,
): SchemaObject | ReferenceObject => {
  const match = /List_(.*)_/.exec(globalKey)
  if (match) {
    const theKey = match[1]
    return {
      type: 'array',
      items: {
        $ref: refLink({ refId: theKey }),
      },
    }
  }

  return {
    $ref: refLink({ refId: globalKey }),
  }
}

/**
 * 清空收集的 schemas
 * 用于确保状态隔离
 */
export function clearSchemas() {
  CollectionSchemas.length = 0
}

/**
 * 获取收集的 schemas 数量
 * @returns Schema 数量
 */
export function getSchemasCount(): number {
  return CollectionSchemas.length
}

/**
 * 获取收集的 schemas 列表
 * @returns Schema 列表的副本
 */
export function getSchemasList(): SchemaMeta[] {
  return [...CollectionSchemas]
}

/**
 * Schema 收集器主函数（向后兼容）
 * @param registry OpenAPI Registry 实例
 */
export const schemaCollector = (registry: OpenAPIRegistry): void => {
  // 注意：Zod OpenAPI 扩展应该在应用启动时就完成，不在这里重复调用

  // 添加全局 schemas
  addGlobalSchemas()

  // 注册所有收集到的 schemas
  registerSchemas(registry)

  logger.info('🎉 Schema 收集完成')
}
