/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-12-25 15:27:50
 * @LastEditTime: 2025-07-21 17:05:12
 * @LastEditors: shaojun
 * @Description: Body装饰器测试
 */

import { describe, expect, it } from 'vitest'
import { z } from 'zod'
import { Body, Controller, RouteConfig } from '../../src/decorators'

describe('body装饰器测试', () => {
  describe('@Body装饰器基础功能', () => {
    it('应该正确存储Body Schema元数据', () => {
      const bodySchema = z.object({
        name: z.string(),
        age: z.number().optional(),
        email: z.string().email(),
      })

      @Controller({ tags: ['test'] })
      class TestController {
        @RouteConfig({
          method: 'post',
          path: '/user',
          summary: '创建用户',
        })
        @Body(bodySchema)
        createUser() {
          return 'success'
        }
      }

      // 验证元数据存储在正确位置
      const metadataKeys = Reflect.getMetadataKeys(TestController.prototype, 'createUser')
      expect(metadataKeys).toContain('swagger:body')

      // 验证存储的Schema
      const storedSchema = Reflect.getMetadata(
        'swagger:body',
        TestController.prototype,
        'createUser',
      )
      expect(storedSchema).toBe(bodySchema)
      expect(storedSchema).toBeInstanceOf(z.ZodObject)
    })

    it('应该支持复杂嵌套的Schema', () => {
      const complexSchema = z.object({
        user: z.object({
          profile: z.object({
            name: z.string(),
            age: z.number(),
          }),
          settings: z.object({
            theme: z.enum(['light', 'dark']),
            notifications: z.boolean(),
          }),
        }),
        metadata: z.record(z.string(), z.any()),
        tags: z.array(z.string()),
      })

      @Controller({ tags: ['complex'] })
      class ComplexController {
        @RouteConfig({
          method: 'post',
          path: '/complex',
          summary: '复杂数据处理',
        })
        @Body(complexSchema)
        processComplexData() {
          return 'processed'
        }
      }

      const storedSchema = Reflect.getMetadata(
        'swagger:body',
        ComplexController.prototype,
        'processComplexData',
      )
      expect(storedSchema).toBe(complexSchema)
    })
  })

  describe('@Body装饰器错误处理', () => {
    it('应该在传入null参数时抛出错误（新的错误处理系统）', () => {
      // Body装饰器现在正确验证参数，null参数会抛出错误
      expect(() => {
        @Controller({ tags: ['test'] })
        class TestController {
          @RouteConfig({ method: 'post', path: '/test' })
          @Body(null as any)
          invalidMethod() {}
        }
      }).toThrow('Schema is required')
    })

    it('应该正确处理装饰器的内部错误检查', () => {
      const bodySchema = z.object({ name: z.string() })

      // 优化后的装饰器直接实现，不再有复杂的错误检查
      // 测试装饰器能正常工作
      expect(() => {
        @Controller({ tags: ['测试'] })
        class TestController {
          @Body(bodySchema)
          testMethod() {}
        }
      }).not.toThrow()
    })
  })

  describe('@Body装饰器组合使用', () => {
    it('应该与其他装饰器正确组合', () => {
      const requestSchema = z.object({
        title: z.string(),
        content: z.string(),
      })

      @Controller({ tags: ['posts'] })
      class PostController {
        @RouteConfig({
          method: 'post',
          path: '/posts',
          summary: '创建文章',
        })
        @Body(requestSchema)
        createPost() {
          return 'created'
        }

        @RouteConfig({
          method: 'put',
          path: '/posts/{id}',
          summary: '更新文章',
        })
        @Body(requestSchema)
        updatePost() {
          return 'updated'
        }
      }

      // 验证两个方法都有正确的Body元数据
      const createSchema = Reflect.getMetadata(
        'swagger:body',
        PostController.prototype,
        'createPost',
      )
      const updateSchema = Reflect.getMetadata(
        'swagger:body',
        PostController.prototype,
        'updatePost',
      )

      expect(createSchema).toBe(requestSchema)
      expect(updateSchema).toBe(requestSchema)
    })
  })

  describe('@Body装饰器类型验证', () => {
    it('应该只接受ZodObject类型参数', () => {
      const validSchema = z.object({ name: z.string() })
      const stringSchema = z.string()
      const arraySchema = z.array(z.string())

      @Controller({ tags: ['validation'] })
      class ValidationController {
        @RouteConfig({ method: 'post', path: '/valid' })
        @Body(validSchema)
        validMethod() {}
      }

      // ZodObject应该工作
      const storedSchema = Reflect.getMetadata(
        'swagger:body',
        ValidationController.prototype,
        'validMethod',
      )
      expect(storedSchema).toBe(validSchema)

      // 非ZodObject类型应该被新的错误处理系统拒绝
      expect(() => {
        @Controller({ tags: ['test'] })
        class TestController {
          @RouteConfig({ method: 'post', path: '/test' })
          @Body(stringSchema as any)
          stringMethod() {}
        }
      }).toThrow('Schema Body: Schema must be a Zod object schema')
    })
  })
})
