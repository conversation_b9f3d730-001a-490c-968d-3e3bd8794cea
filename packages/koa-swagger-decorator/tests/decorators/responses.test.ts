/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-12-25 16:05:00
 * @LastEditTime: 2025-07-17 01:19:42
 * @LastEditors: shaojun
 * @Description: Responses装饰器测试
 */

import { describe, expect, it } from 'vitest'
import { z } from 'zod'
import { Body, Controller, Responses, RouteConfig } from '../../src/decorators'

describe('responses装饰器测试', () => {
  describe('@Responses装饰器基础功能', () => {
    it('应该正确存储Responses Schema元数据', () => {
      const responseSchema = z.object({
        id: z.string(),
        name: z.string(),
        email: z.string().email(),
        createdAt: z.string(),
      })

      @Controller({ tags: ['test'] })
      class TestController {
        @RouteConfig({
          method: 'get',
          path: '/user/{id}',
          summary: '获取用户',
        })
        @Responses(responseSchema)
        getUser() {
          return 'user'
        }
      }

      // 验证元数据存储在正确位置
      const metadataKeys = Reflect.getMetadataKeys(TestController.prototype, 'getUser')
      expect(metadataKeys).toContain('swagger:responses')

      // 验证存储的Schema - 现在是包装后的格式
      const storedSchema = Reflect.getMetadata(
        'swagger:responses',
        TestController.prototype,
        'getUser',
      )
      expect(storedSchema).toEqual({
        200: {
          description: 'Success',
          schema: responseSchema,
        },
      })
      expect(storedSchema[200].schema).toBeInstanceOf(z.ZodObject)
    })

    it('应该支持简单数据类型Schema', () => {
      // 定义schemas作为变量
      const stringSchema = z.object({ message: z.string() })
      const numberSchema = z.object({ count: z.number() })
      const booleanSchema = z.object({ success: z.boolean() })
      const arraySchema = z.object({ items: z.array(z.string()) })

      @Controller({ tags: ['简单类型'] })
      class SimpleController {
        @RouteConfig({ method: 'get', path: '/string' })
        @Responses(stringSchema)
        getString() {}

        @RouteConfig({ method: 'get', path: '/number' })
        @Responses(numberSchema)
        getNumber() {}

        @RouteConfig({ method: 'get', path: '/boolean' })
        @Responses(booleanSchema)
        getBoolean() {}

        @RouteConfig({ method: 'get', path: '/array' })
        @Responses(arraySchema)
        getArray() {}
      }

      const testCases = [
        { method: 'getString', schema: stringSchema },
        { method: 'getNumber', schema: numberSchema },
        { method: 'getBoolean', schema: booleanSchema },
        { method: 'getArray', schema: arraySchema },
      ]

      testCases.forEach(({ method, schema }) => {
        const storedSchema = Reflect.getMetadata(
          'swagger:responses',
          SimpleController.prototype,
          method,
        )
        expect(storedSchema).toEqual({
          200: {
            description: 'Success',
            schema,
          },
        })
      })
    })
  })

  describe('@Responses装饰器复杂Schema', () => {
    it('应该支持复杂嵌套的Schema', () => {
      const complexSchema = z.object({
        user: z.object({
          id: z.string(),
          profile: z.object({
            name: z.string(),
            email: z.string().email(),
            avatar: z.string().url().optional(),
            preferences: z.object({
              theme: z.enum(['light', 'dark']),
              language: z.string().default('zh-CN'),
              notifications: z.object({
                email: z.boolean(),
                push: z.boolean(),
                sms: z.boolean(),
              }),
            }),
          }),
          roles: z.array(z.string()),
          permissions: z.array(z.string()),
        }),
        metadata: z.object({
          lastLogin: z.string().datetime(),
          loginCount: z.number(),
          createdAt: z.string().datetime(),
          updatedAt: z.string().datetime(),
        }),
        settings: z.record(z.string(), z.any()),
      })

      @Controller({ tags: ['复杂Schema'] })
      class ComplexController {
        @RouteConfig({
          method: 'get',
          path: '/user/complete/{id}',
          summary: '获取完整用户信息',
        })
        @Responses(complexSchema)
        getCompleteUser() {
          return 'complex user data'
        }
      }

      const storedSchema = Reflect.getMetadata(
        'swagger:responses',
        ComplexController.prototype,
        'getCompleteUser',
      )
      expect(storedSchema).toEqual({
        200: {
          description: 'Success',
          schema: complexSchema,
        },
      })
    })

    it('应该支持联合类型和可选字段', () => {
      const unionSchema = z.object({
        status: z.enum(['success', 'error', 'pending']),
        data: z.union([
          z.object({ result: z.string() }),
          z.object({ error: z.string() }),
        ]).optional(),
        timestamp: z.number(),
        metadata: z.object({
          requestId: z.string(),
          version: z.string(),
          experimental: z.boolean().optional(),
        }),
      })

      @Controller({ tags: ['联合类型'] })
      class UnionController {
        @RouteConfig({
          method: 'post',
          path: '/process',
          summary: '处理请求',
        })
        @Responses(unionSchema)
        processRequest() {
          return 'processed'
        }
      }

      const storedSchema = Reflect.getMetadata(
        'swagger:responses',
        UnionController.prototype,
        'processRequest',
      )
      expect(storedSchema).toEqual({
        200: {
          description: 'Success',
          schema: unionSchema,
        },
      })
    })

    it('应该支持数组和分页响应', () => {
      const userSchema = z.object({
        id: z.string(),
        name: z.string(),
        email: z.string(),
      })

      const paginatedSchema = z.object({
        data: z.array(userSchema),
        pagination: z.object({
          page: z.number().min(1),
          limit: z.number().min(1).max(100),
          total: z.number().min(0),
          totalPages: z.number().min(0),
          hasNext: z.boolean(),
          hasPrev: z.boolean(),
        }),
        meta: z.object({
          timestamp: z.string(),
          requestId: z.string(),
        }),
      })

      @Controller({ tags: ['分页响应'] })
      class PaginationController {
        @RouteConfig({
          method: 'get',
          path: '/users',
          summary: '分页获取用户列表',
        })
        @Responses(paginatedSchema)
        getUserList() {
          return 'paginated users'
        }
      }

      const storedSchema = Reflect.getMetadata(
        'swagger:responses',
        PaginationController.prototype,
        'getUserList',
      )

      expect(storedSchema).toEqual({
        200: {
          description: 'Success',
          schema: paginatedSchema,
        },
      })
    })
  })

  describe('@Responses装饰器错误处理', () => {
    it('应该在传入null参数时抛出错误（新的错误处理系统）', () => {
      // Responses装饰器现在正确验证参数，null参数会抛出错误
      expect(() => {
        @Controller({ tags: ['test'] })
        class TestController {
          @RouteConfig({ method: 'get', path: '/test' })
          @Responses(null as any)
          testMethod() {}
        }
      }).toThrow('Responses configuration is required')
    })

    it('应该正确处理装饰器的内部错误检查', () => {
      const responseSchema = z.object({ result: z.string() })

      // 优化后的装饰器直接实现，不再有复杂的错误检查
      // 测试装饰器能正常工作
      expect(() => {
        @Controller({ tags: ['测试'] })
        class TestController {
          @Responses(responseSchema)
          testMethod() {}
        }
      }).not.toThrow()
    })
  })

  describe('@Responses装饰器与其他装饰器组合', () => {
    it('应该与RouteConfig和Body装饰器正确组合', () => {
      const requestSchema = z.object({
        title: z.string(),
        content: z.string(),
        tags: z.array(z.string()),
      })

      const responseSchema = z.object({
        id: z.string(),
        title: z.string(),
        content: z.string(),
        tags: z.array(z.string()),
        createdAt: z.string(),
        updatedAt: z.string(),
        author: z.object({
          id: z.string(),
          name: z.string(),
        }),
      })

      @Controller({ tags: ['组合测试'] })
      class CombinedController {
        @RouteConfig({
          method: 'post',
          path: '/posts',
          summary: '创建文章',
          description: '创建新文章',
        })
        @Body(requestSchema)
        @Responses(responseSchema)
        createPost() {
          return 'post created'
        }
      }

      // 验证RouteConfig
      const routeConfig = Reflect.getMetadata(
        'swagger:routeConfig',
        CombinedController.prototype,
        'createPost',
      )
      expect(routeConfig.method).toBe('post')
      expect(routeConfig.path).toBe('/posts')

      // 验证Body
      const bodyConfig = Reflect.getMetadata(
        'swagger:body',
        CombinedController.prototype,
        'createPost',
      )
      expect(bodyConfig).toBe(requestSchema)

      // 验证Responses
      const responsesConfig = Reflect.getMetadata(
        'swagger:responses',
        CombinedController.prototype,
        'createPost',
      )
      expect(responsesConfig).toEqual({
        200: {
          description: 'Success',
          schema: responseSchema,
        },
      })
    })

    it('应该支持同一控制器中的多个响应Schema', () => {
      const userSchema = z.object({
        id: z.string(),
        name: z.string(),
        email: z.string(),
      })

      const postSchema = z.object({
        id: z.string(),
        title: z.string(),
        content: z.string(),
      })

      const commentSchema = z.object({
        id: z.string(),
        content: z.string(),
        authorId: z.string(),
      })

      @Controller({ tags: ['多响应Schema'] })
      class MultiResponseController {
        @RouteConfig({ method: 'get', path: '/users/{id}' })
        @Responses(userSchema)
        getUser() {}

        @RouteConfig({ method: 'get', path: '/posts/{id}' })
        @Responses(postSchema)
        getPost() {}

        @RouteConfig({ method: 'get', path: '/comments/{id}' })
        @Responses(commentSchema)
        getComment() {}
      }

      // 验证每个方法都有正确的响应Schema
      const userResponse = Reflect.getMetadata(
        'swagger:responses',
        MultiResponseController.prototype,
        'getUser',
      )
      const postResponse = Reflect.getMetadata(
        'swagger:responses',
        MultiResponseController.prototype,
        'getPost',
      )
      const commentResponse = Reflect.getMetadata(
        'swagger:responses',
        MultiResponseController.prototype,
        'getComment',
      )

      expect(userResponse).toEqual({
        200: { description: 'Success', schema: userSchema },
      })
      expect(postResponse).toEqual({
        200: { description: 'Success', schema: postSchema },
      })
      expect(commentResponse).toEqual({
        200: { description: 'Success', schema: commentSchema },
      })
    })
  })

  describe('@Responses装饰器类型验证', () => {
    it('应该支持Zod的所有基础类型', () => {
      const zodTypes = {
        string: z.object({ value: z.string() }),
        number: z.object({ value: z.number() }),
        boolean: z.object({ value: z.boolean() }),
        date: z.object({ value: z.date() }),
        array: z.object({ value: z.array(z.string()) }),
        object: z.object({ value: z.object({ nested: z.string() }) }),
        enum: z.object({ value: z.enum(['a', 'b', 'c']) }),
        union: z.object({ value: z.union([z.string(), z.number()]) }),
        optional: z.object({ value: z.string().optional() }),
        nullable: z.object({ value: z.string().nullable() }),
      }

      @Controller({ tags: ['Zod类型测试'] })
      class ZodTypeController {
        @RouteConfig({ method: 'get', path: '/string' })
        @Responses(zodTypes.string)
        getString() {}

        @RouteConfig({ method: 'get', path: '/number' })
        @Responses(zodTypes.number)
        getNumber() {}

        @RouteConfig({ method: 'get', path: '/boolean' })
        @Responses(zodTypes.boolean)
        getBoolean() {}

        @RouteConfig({ method: 'get', path: '/date' })
        @Responses(zodTypes.date)
        getDate() {}

        @RouteConfig({ method: 'get', path: '/array' })
        @Responses(zodTypes.array)
        getArray() {}

        @RouteConfig({ method: 'get', path: '/object' })
        @Responses(zodTypes.object)
        getObject() {}

        @RouteConfig({ method: 'get', path: '/enum' })
        @Responses(zodTypes.enum)
        getEnum() {}

        @RouteConfig({ method: 'get', path: '/union' })
        @Responses(zodTypes.union)
        getUnion() {}

        @RouteConfig({ method: 'get', path: '/optional' })
        @Responses(zodTypes.optional)
        getOptional() {}

        @RouteConfig({ method: 'get', path: '/nullable' })
        @Responses(zodTypes.nullable)
        getNullable() {}
      }

      const methods = Object.keys(zodTypes).map((type) => `get${type.charAt(0).toUpperCase() + type.slice(1)}`)

      methods.forEach((methodName, index) => {
        const storedSchema = Reflect.getMetadata(
          'swagger:responses',
          ZodTypeController.prototype,
          methodName,
        )
        const expectedSchema = Object.values(zodTypes)[index]
        expect(storedSchema).toEqual({
          200: {
            description: 'Success',
            schema: expectedSchema,
          },
        })
      })
    })

    it('应该支持复杂的Zod验证规则', () => {
      const validationSchema = z.object({
        email: z.string().email(),
        url: z.string().url(),
        phone: z.string().regex(/^\+?[\d\s()-]+$/), // Unescaped parentheses, escaped hyphen
        age: z.number().min(0).max(120),
        name: z.string().min(2).max(50),
        tags: z.array(z.string()).min(1).max(10),
        score: z.number().multipleOf(0.1),
        metadata: z.record(z.string(), z.any()),
        createdAt: z.string().datetime(),
        isActive: z.boolean().default(true),
      })

      @Controller({ tags: ['验证规则'] })
      class ValidationController {
        @RouteConfig({
          method: 'post',
          path: '/validate',
          summary: '验证数据',
        })
        @Responses(validationSchema)
        validateData() {
          return 'validated'
        }
      }

      const storedSchema = Reflect.getMetadata(
        'swagger:responses',
        ValidationController.prototype,
        'validateData',
      )
      expect(storedSchema).toEqual({
        200: {
          description: 'Success',
          schema: validationSchema,
        },
      })
    })
  })

  describe('@Responses装饰器实际应用场景', () => {
    it('应该支持API错误响应格式', () => {
      const errorResponseSchema = z.object({
        error: z.object({
          code: z.string(),
          message: z.string(),
          details: z.record(z.string(), z.any()).optional(),
          timestamp: z.string(),
          path: z.string(),
          requestId: z.string(),
        }),
        status: z.number(),
        success: z.literal(false),
      })

      @Controller({ tags: ['错误处理'] })
      class ErrorController {
        @RouteConfig({
          method: 'get',
          path: '/error-example',
          summary: '错误响应示例',
        })
        @Responses(errorResponseSchema)
        getError() {
          return 'error response'
        }
      }

      const storedSchema = Reflect.getMetadata(
        'swagger:responses',
        ErrorController.prototype,
        'getError',
      )
      expect(storedSchema).toEqual({
        200: {
          description: 'Success',
          schema: errorResponseSchema,
        },
      })
    })

    it('应该支持文件上传响应格式', () => {
      const fileUploadResponseSchema = z.object({
        files: z.array(z.object({
          id: z.string(),
          filename: z.string(),
          originalName: z.string(),
          mimeType: z.string(),
          size: z.number(),
          url: z.string().url(),
          downloadUrl: z.string().url(),
          metadata: z.object({
            uploadedAt: z.string().datetime(),
            uploadedBy: z.string(),
            checksum: z.string(),
          }),
        })),
        summary: z.object({
          totalFiles: z.number(),
          totalSize: z.number(),
          uploadDuration: z.number(),
        }),
      })

      @Controller({ tags: ['文件上传'] })
      class FileController {
        @RouteConfig({
          method: 'post',
          path: '/upload',
          summary: '文件上传',
        })
        @Responses(fileUploadResponseSchema)
        uploadFiles() {
          return 'files uploaded'
        }
      }

      const storedSchema = Reflect.getMetadata(
        'swagger:responses',
        FileController.prototype,
        'uploadFiles',
      )
      expect(storedSchema).toEqual({
        200: {
          description: 'Success',
          schema: fileUploadResponseSchema,
        },
      })
    })
  })
})
