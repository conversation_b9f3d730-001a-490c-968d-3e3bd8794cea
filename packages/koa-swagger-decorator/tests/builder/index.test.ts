/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2024-12-25 14:25:57
 * @LastEditTime: 2025-07-21 17:03:14
 * @LastEditors: shaojun
 * @Description:
 */

import { beforeEach, describe, expect, it } from 'vitest'
import { z } from 'zod'
import { CollectionSchemas, generateSwaggerDoc } from '../../src/builder'
import { Body, Controller, Responses, RouteConfig } from '../../src/decorators'
import { useRegistry } from '../../src/registry'
import { globalSchemas, GlobalSchemasKeyEnum } from '../case'

const { getRegistry, createRegistry, resetRegistry } = useRegistry()

describe('swagger Builder', () => {
  beforeEach(() => {
    // 清理元数据缓存和全局schemas
    CollectionSchemas.length = 0
    Reflect.getMetadataKeys(Object.prototype).forEach((key) => {
      Reflect.deleteMetadata(key, Object.prototype)
    })
    // 重置 registry
    resetRegistry()
  })

  describe('generateSwaggerDoc', () => {
    it('should generate basic swagger doc with controller and route', () => {
      @Controller({
        tags: ['用户管理'],
        paths: {
          parameters: [
            {
              name: 'Authorization',
              in: 'header',
              required: true,
              schema: { type: 'string' },
            },
          ],
        },
        components: {
          // 全局参数
          parameters: {
            'ALL-PARAMS': {
              name: 'ALL-PARAMS',
              in: 'header',
              required: true,
              schema: {
                type: 'string',
              },
            },
          },
        },
      })
      class UserController {
        @RouteConfig({
          method: 'get',
          path: '/user/info',
          summary: '获取用户信息',
          description: '获取当前登录用户的信息',
        })
        async getUserInfo() {}
      }

      const { object: docs } = generateSwaggerDoc(getRegistry(), [
        UserController,
      ])
      expect(docs).toMatchInlineSnapshot(`
        {
          "components": {
            "parameters": {
              "0": {
                "in": "header",
                "name": "ALL-PARAMS",
                "required": true,
                "schema": {
                  "type": "string",
                },
              },
              "1": {
                "in": "header",
                "name": "Authorization",
                "required": true,
                "schema": {
                  "type": "string",
                },
              },
            },
            "schemas": {},
          },
          "info": {
            "title": "Swagger API",
            "version": "3.0.0",
          },
          "openapi": "3.0.0",
          "paths": {
            "/user/info": {
              "get": {
                "description": "获取当前登录用户的信息",
                "methodName": "getUserInfo",
                "operationId": "getUserInfo",
                "parameters": [
                  {
                    "in": "header",
                    "name": "ALL-PARAMS",
                    "required": true,
                    "schema": {
                      "type": "string",
                    },
                  },
                  {
                    "in": "header",
                    "name": "Authorization",
                    "required": true,
                    "schema": {
                      "type": "string",
                    },
                  },
                ],
                "responses": {
                  "200": {
                    "description": "Success",
                  },
                },
                "summary": "获取用户信息",
                "tags": [
                  "用户管理",
                ],
              },
            },
          },
        }
      `)
      expect(docs.openapi).toBe('3.0.0')
      expect(docs.paths).toBeDefined()
      expect(docs.paths['/user/info']).toBeDefined()

      const route = docs.paths['/user/info'].get
      expect(route).toBeDefined()
      expect(route).toMatchObject({
        tags: ['用户管理'],
        operationId: 'getUserInfo',
        summary: '获取用户信息',
        description: '获取当前登录用户的信息',
        parameters: [
          {
            name: 'ALL-PARAMS',
            in: 'header',
            required: true,
            schema: { type: 'string' },
          },
          {
            name: 'Authorization',
            in: 'header',
            required: true,
            schema: { type: 'string' },
          },
        ],
      })
    })

    it('should handle body and response schemas correctly', () => {
      // 1. 定义测试用的 Schema
      const UserCreateSchema = z.object({
        username: z.string().min(3).max(20),
        password: z.string().min(6),
      })

      const UserResponseSchema = z.object({
        id: z.number(),
        username: z.string(),
        createdAt: z.string(),
      })

      // 2. 定义测试用的控制器
      @Controller({ tags: ['用户管理'] })
      class UserController {
        @RouteConfig({
          method: 'post',
          path: '/user/create',
          summary: '创建用户',
        })
        @Body(UserCreateSchema)
        @Responses(UserResponseSchema)
        async createUser() {}
      }

      // 3. 生成文档
      const { object: docs } = generateSwaggerDoc(
        getRegistry(),
        [UserController],
        undefined,
        globalSchemas,
      )

      // 4. 验证路由配置
      const route = docs.paths['/user/create'].post
      expect(route).toBeDefined()

      // 5. 验证请求体
      expect(route.requestBody).toMatchObject({
        required: true,
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/CreateUserRequest',
            },
          },
        },
      })

      // 6. 验证响应
      expect(route.responses['200']).toMatchObject({
        description: 'Success',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/R_CreateUserVO_',
            },
          },
        },
      })

      // 7. 验证 Schema 定义
      expect(docs.components.schemas).toBeDefined()
      expect(docs.components.schemas.CreateUserRequest).toBeDefined()
      expect(docs.components.schemas.R_CreateUserVO_).toBeDefined()
    })

    it('should handle path prefix correctly', () => {
      @Controller({ tags: ['测试'] })
      class TestController {
        @RouteConfig({
          method: 'get',
          path: '/test',
          summary: '测试接口',
        })
        async test() {}
      }

      const { object: docs } = generateSwaggerDoc(
        getRegistry(),
        [TestController],
        '/api/v1',
      )
      expect(docs.paths['/api/v1/test']).toBeDefined()
      expect(docs.paths['/test']).toBeUndefined()
    })

    it('should merge global schemas', () => {
      // 1. 定义全局 Schema
      const CommonResponseSchema = z.object({
        code: z.number(),
        message: z.string(),
        data: z.any(),
      })

      // const globalSchemas = {
      //   CommonResponse: CommonResponseSchema,
      // };

      // 2. 定义测试控制器
      @Controller({ tags: ['测试'] })
      class TestController {
        @RouteConfig({
          method: 'get',
          path: '/test',
        })
        @Responses(GlobalSchemasKeyEnum.StringData) // 使用全局 Schema
        async test() {}
      }

      // 3. 生成文档
      const { object: docs } = generateSwaggerDoc(
        getRegistry(),
        [TestController],
        undefined,
        globalSchemas,
      )

      // 4. 验证全局 Schema
      expect(docs.components.schemas.R_StringData_).toBeDefined()

      // 5. 验证响应引用
      const route = docs.paths['/test'].get
      expect(route).toMatchInlineSnapshot(`
        {
          "methodName": "test",
          "operationId": "test",
          "parameters": [],
          "responses": {
            "200": {
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/R_StringData_",
                  },
                },
              },
              "description": "Success",
            },
          },
          "tags": [
            "测试",
          ],
        }
      `)
      expect(
        route.responses['200'].content['application/json'].schema.$ref,
      ).toBe('#/components/schemas/R_StringData_')
    })

    it('should handle multiple controllers', () => {
      @Controller({ tags: ['用户'] })
      class UserController {
        @RouteConfig({
          method: 'get',
          path: '/user',
        })
        @Responses(GlobalSchemasKeyEnum.UserInfo)
        async getUser() {}
      }

      @Controller({ tags: ['订单'] })
      class OrderController {
        @RouteConfig({
          method: 'get',
          path: '/order',
        })
        async getOrder() {}
      }

      const { object: docs } = generateSwaggerDoc(
        getRegistry(),
        [UserController, OrderController],
        undefined,
        globalSchemas,
      )

      expect(docs.paths['/user']).toBeDefined()
      expect(docs.paths['/order']).toBeDefined()

      expect(docs.paths['/user'].get.tags).toEqual(['用户'])
      expect(docs.paths['/order'].get.tags).toEqual(['订单'])

      expect(docs).toMatchInlineSnapshot(`
        {
          "components": {
            "parameters": {},
            "schemas": {
              "AppInfo": {
                "properties": {
                  "appName": {
                    "type": "string",
                  },
                  "bundleId": {
                    "type": "string",
                  },
                  "bundleName": {
                    "type": "string",
                  },
                  "currentVersion": {
                    "type": "string",
                  },
                  "id": {
                    "type": "string",
                  },
                  "platform": {
                    "type": "string",
                  },
                },
                "required": [
                  "id",
                  "platform",
                  "bundleId",
                  "bundleName",
                  "appName",
                  "currentVersion",
                ],
                "type": "object",
              },
              "R_StringData_": {
                "properties": {
                  "data": {
                    "example": "",
                    "type": "string",
                  },
                  "resultCode": {
                    "example": 200,
                    "type": "number",
                  },
                  "resultMessage": {
                    "example": "success",
                    "type": "string",
                  },
                },
                "required": [
                  "resultCode",
                  "resultMessage",
                  "data",
                ],
                "type": "object",
              },
              "R_UserInfo_": {
                "properties": {
                  "data": {
                    "$ref": "#/components/schemas/UserInfo",
                  },
                  "resultCode": {
                    "example": 200,
                    "type": "number",
                  },
                  "resultMessage": {
                    "example": "success",
                    "type": "string",
                  },
                },
                "required": [
                  "resultCode",
                  "resultMessage",
                  "data",
                ],
                "type": "object",
              },
              "StringData": {
                "example": "",
                "type": "string",
              },
              "UserInfo": {
                "properties": {
                  "avatar": {
                    "example": "",
                    "type": "string",
                  },
                  "id": {
                    "example": "123",
                    "type": "string",
                  },
                  "nickname": {
                    "example": "John Doe",
                    "type": "string",
                  },
                  "token": {
                    "example": "123456",
                    "type": "string",
                  },
                  "username": {
                    "example": "John Doe",
                    "type": "string",
                  },
                },
                "required": [
                  "id",
                  "username",
                  "token",
                ],
                "type": "object",
              },
            },
          },
          "info": {
            "title": "Swagger API",
            "version": "3.0.0",
          },
          "openapi": "3.0.0",
          "paths": {
            "/order": {
              "get": {
                "methodName": "getOrder",
                "operationId": "getOrder",
                "parameters": [],
                "responses": {
                  "200": {
                    "description": "Success",
                  },
                },
                "tags": [
                  "订单",
                ],
              },
            },
            "/user": {
              "get": {
                "methodName": "getUser",
                "operationId": "getUser",
                "parameters": [],
                "responses": {
                  "200": {
                    "content": {
                      "application/json": {
                        "schema": {
                          "$ref": "#/components/schemas/R_UserInfo_",
                        },
                      },
                    },
                    "description": "Success",
                  },
                },
                "tags": [
                  "用户",
                ],
              },
            },
          },
        }
      `)
    })

    // ==================== 新增测试用例 ====================

    it('should handle complex nested schemas', () => {
      const NestedSchema = z.object({
        user: z.object({
          profile: z.object({
            personal: z.object({
              name: z.string(),
              age: z.number(),
              email: z.string().email(),
            }),
            preferences: z.object({
              theme: z.enum(['light', 'dark']),
              language: z.string().default('zh-CN'),
              notifications: z.boolean(),
            }),
          }),
          permissions: z.array(z.string()),
        }),
        metadata: z.record(z.string(), z.any()),
      })

      @Controller({ tags: ['复杂Schema测试'] })
      class ComplexController {
        @RouteConfig({
          method: 'post',
          path: '/complex',
          summary: '复杂嵌套Schema测试',
        })
        @Body(NestedSchema)
        @Responses(NestedSchema)
        async complexOperation() {}
      }

      const { object: docs } = generateSwaggerDoc(getRegistry(), [
        ComplexController,
      ])

      const route = docs.paths['/complex'].post
      expect(route).toBeDefined()
      expect(route.requestBody).toBeDefined()
      expect(route.responses['200']).toBeDefined()
      expect(docs.components.schemas.ComplexOperationRequest).toBeDefined()
      expect(docs.components.schemas.R_ComplexOperationVO_).toBeDefined()
    })

    it('should handle array schemas', () => {
      const UserListSchema = z.array(
        z.object({
          id: z.number(),
          name: z.string(),
          email: z.string().email(),
        }),
      )

      const CreateUsersSchema = z.object({
        users: z.array(
          z.object({
            name: z.string(),
            email: z.string().email(),
            role: z.enum(['admin', 'user', 'guest']),
          }),
        ),
        batchId: z.string().uuid(),
      })

      @Controller({ tags: ['数组Schema测试'] })
      class ArrayController {
        @RouteConfig({
          method: 'get',
          path: '/users',
          summary: '获取用户列表',
        })
        @Responses(UserListSchema)
        async getUserList() {}

        @RouteConfig({
          method: 'post',
          path: '/users/batch',
          summary: '批量创建用户',
        })
        @Body(CreateUsersSchema)
        @Responses(z.object({ created: z.number(), failed: z.number() }))
        async createUsersBatch() {}
      }

      const { object: docs } = generateSwaggerDoc(getRegistry(), [
        ArrayController,
      ])

      expect(docs.paths['/users'].get).toBeDefined()
      expect(docs.paths['/users/batch'].post).toBeDefined()
      expect(Object.keys(docs.components.schemas)).toMatchInlineSnapshot(`
        [
          "R_GetUserListVO_",
          "R_CreateUsersBatchVO_",
          "GetUserListVO",
          "CreateUsersBatchRequest",
          "CreateUsersBatchVO",
        ]
      `)

      expect(docs.components.schemas.R_GetUserListVO_).toBeDefined()
      expect(docs.components.schemas.CreateUsersBatchRequest).toBeDefined()
    })

    it('should handle optional and nullable fields', () => {
      const OptionalSchema = z.object({
        required: z.string(),
        optional: z.string().optional(),
        nullable: z.string().nullable(),
        optionalNullable: z.string().optional().nullable(),
        defaultValue: z.string().default('default'),
        union: z.union([z.string(), z.number()]),
        enum: z.enum(['A', 'B', 'C']).optional(),
      })

      @Controller({ tags: ['可选字段测试'] })
      class OptionalController {
        @RouteConfig({
          method: 'post',
          path: '/optional',
          summary: '可选字段测试',
        })
        @Body(OptionalSchema)
        @Responses(OptionalSchema)
        async optionalFields() {}
      }

      const { object: docs } = generateSwaggerDoc(getRegistry(), [
        OptionalController,
      ])

      const route = docs.paths['/optional'].post
      expect(route).toBeDefined()
      expect(docs.components.schemas.OptionalFieldsRequest).toBeDefined()
    })

    it('should handle advanced route configurations', () => {
      @Controller({ tags: ['高级配置'] })
      class AdvancedController {
        @RouteConfig({
          method: 'put',
          path: '/advanced/{id}',
          summary: '高级配置测试',
          description: '包含各种高级配置的测试接口',
          operationId: 'customOperationId',
          deprecated: true,
          tags: ['覆盖标签'],
          parameters: {
            id: {
              name: 'id',
              in: 'path',
              required: true,
              schema: { type: 'string', pattern: '^[0-9]+$' },
              description: '资源ID',
            },
            version: {
              name: 'version',
              in: 'query',
              required: false,
              schema: { type: 'string' },
              description: 'API版本',
            },
          },
          security: [{ Bearer: [] }],
          servers: [{ url: 'https://api.example.com', description: '生产服务器' }],
          externalDocs: {
            description: '更多文档',
            url: 'https://docs.example.com',
          },
        })
        @Body(z.object({ data: z.any() }))
        @Responses(z.object({ success: z.boolean() }))
        async advancedOperation() {}
      }

      const { object: docs } = generateSwaggerDoc(getRegistry(), [
        AdvancedController,
      ])

      const route = docs.paths['/advanced/{id}'].put
      expect(route).toBeDefined()
      expect(route.operationId).toBe('customOperationId')
      expect(route.deprecated).toBe(true)
      expect(route.tags).toEqual(['高级配置', '覆盖标签'])
      expect(route.security).toEqual([{ Bearer: [] }])
      expect(route.servers).toEqual([
        { url: 'https://api.example.com', description: '生产服务器' },
      ])
      expect(route.externalDocs).toEqual({
        description: '更多文档',
        url: 'https://docs.example.com',
      })
    })

    it('should handle custom response status codes', () => {
      @Controller({ tags: ['自定义响应'] })
      class CustomResponseController {
        @RouteConfig({
          method: 'post',
          path: '/custom-response',
          summary: '自定义响应状态码',
          responses: {
            201: { description: 'Created successfully' },
            400: { description: 'Bad request' },
            404: { description: 'Not found' },
            500: { description: 'Internal server error' },
          },
        })
        @Body(z.object({ name: z.string() }))
        async customResponse() {}
      }

      const { object: docs } = generateSwaggerDoc(getRegistry(), [
        CustomResponseController,
      ])

      const route = docs.paths['/custom-response'].post
      expect(route).toBeDefined()
      expect(route.responses['201']).toEqual({ description: 'Created successfully' })
      expect(route.responses['400']).toEqual({ description: 'Bad request' })
      expect(route.responses['404']).toEqual({ description: 'Not found' })
      expect(route.responses['500']).toEqual({ description: 'Internal server error' })
    })
  })

  describe('边界情况和错误处理', () => {
    it('should handle empty controller', () => {
      @Controller({ tags: ['空控制器'] })
      class EmptyController {}

      const { object: docs } = generateSwaggerDoc(getRegistry(), [
        EmptyController,
      ])

      expect(docs.openapi).toBe('3.0.0')
      expect(Object.keys(docs.paths)).toHaveLength(0)
    })

    it('should handle controller without decorator', () => {
      class PlainController {
        async plainMethod() {}
      }

      const { object: docs } = generateSwaggerDoc(getRegistry(), [
        PlainController,
      ])

      expect(docs.openapi).toBe('3.0.0')
      expect(Object.keys(docs.paths)).toHaveLength(0)
    })

    it('should handle method without route decorator', () => {
      @Controller({ tags: ['部分装饰器'] })
      class PartialController {
        @RouteConfig({
          method: 'get',
          path: '/decorated',
          summary: '有装饰器的方法',
        })
        async decoratedMethod() {}

        async plainMethod() {}
      }

      const { object: docs } = generateSwaggerDoc(getRegistry(), [
        PartialController,
      ])

      expect(docs.paths['/decorated']).toBeDefined()
      expect(Object.keys(docs.paths)).toHaveLength(1)
    })

    it('should handle duplicate operation IDs', () => {
      @Controller({ tags: ['重复ID测试'] })
      class DuplicateController {
        @RouteConfig({
          method: 'get',
          path: '/test1',
          operationId: 'duplicateId',
        })
        async method1() {}

        @RouteConfig({
          method: 'post',
          path: '/test2',
          operationId: 'duplicateId',
        })
        async method2() {}
      }

      const { object: docs } = generateSwaggerDoc(getRegistry(), [
        DuplicateController,
      ])

      expect(docs.paths['/test1']).toBeDefined()
      expect(docs.paths['/test2']).toBeDefined()
      expect(docs.paths['/test1'].get.operationId).toBe('duplicateId')
      expect(docs.paths['/test2'].post.operationId).toBe('duplicateId')
    })

    it('should handle very large schemas', () => {
      const largeProperties = {}
      for (let i = 0; i < 100; i++) {
        largeProperties[`field${i}`] = z.string().optional()
      }

      const LargeSchema = z.object(largeProperties)

      @Controller({ tags: ['大型Schema'] })
      class LargeSchemaController {
        @RouteConfig({
          method: 'post',
          path: '/large',
          summary: '大型Schema测试',
        })
        @Body(LargeSchema)
        @Responses(LargeSchema)
        async largeSchema() {}
      }

      const { object: docs } = generateSwaggerDoc(getRegistry(), [
        LargeSchemaController,
      ])

      expect(docs.paths['/large']).toBeDefined()
      expect(docs.components.schemas.LargeSchemaRequest).toBeDefined()
      expect(docs.components.schemas.R_LargeSchemaVO_).toBeDefined()
    })

    it('should handle multiple HTTP methods on same path', () => {
      @Controller({ tags: ['多方法'] })
      class MultiMethodController {
        @RouteConfig({
          method: 'get',
          path: '/resource',
          summary: '获取资源',
        })
        async getResource() {}

        @RouteConfig({
          method: 'post',
          path: '/resource',
          summary: '创建资源',
        })
        @Body(z.object({ name: z.string() }))
        async createResource() {}

        @RouteConfig({
          method: 'put',
          path: '/resource',
          summary: '更新资源',
        })
        @Body(z.object({ id: z.string(), name: z.string() }))
        async updateResource() {}

        @RouteConfig({
          method: 'delete',
          path: '/resource',
          summary: '删除资源',
        })
        async deleteResource() {}
      }

      const { object: docs } = generateSwaggerDoc(getRegistry(), [
        MultiMethodController,
      ])

      const resourcePath = docs.paths['/resource']
      expect(resourcePath.get).toBeDefined()
      expect(resourcePath.post).toBeDefined()
      expect(resourcePath.put).toBeDefined()
      expect(resourcePath.delete).toBeDefined()

      expect(resourcePath.get.summary).toBe('获取资源')
      expect(resourcePath.post.summary).toBe('创建资源')
      expect(resourcePath.put.summary).toBe('更新资源')
      expect(resourcePath.delete.summary).toBe('删除资源')
    })
  })

  describe('schema集合和注册', () => {
    it('should handle schema deduplication', () => {
      const SharedSchema = z.object({
        id: z.string(),
        name: z.string(),
      })

      @Controller({ tags: ['Schema去重'] })
      class DeduplicationController {
        @RouteConfig({
          method: 'post',
          path: '/test1',
        })
        @Body(SharedSchema)
        @Responses(SharedSchema)
        async test1() {}

        @RouteConfig({
          method: 'post',
          path: '/test2',
        })
        @Body(SharedSchema)
        @Responses(SharedSchema)
        async test2() {}
      }

      const { object: docs } = generateSwaggerDoc(getRegistry(), [
        DeduplicationController,
      ])

      // 验证Schema被正确生成
      expect(docs.components.schemas.Test1Request).toBeDefined()
      expect(docs.components.schemas.Test2Request).toBeDefined()
      expect(docs.components.schemas.R_Test1VO_).toBeDefined()
      expect(docs.components.schemas.R_Test2VO_).toBeDefined()
    })

    it('should handle global schema override', () => {
      const customGlobals = {
        CustomGlobal: z.object({
          customField: z.string(),
        }),
      }

      @Controller({ tags: ['全局覆盖'] })
      class GlobalOverrideController {
        @RouteConfig({
          method: 'get',
          path: '/global',
        })
        @Responses('CustomGlobal')
        async useGlobal() {}
      }

      const { object: docs } = generateSwaggerDoc(
        getRegistry(),
        [GlobalOverrideController],
        undefined,
        customGlobals,
      )

      expect(docs.components.schemas.CustomGlobal).toBeDefined()
      expect(docs.components.schemas.R_CustomGlobal_).toBeDefined()
    })
  })

  describe('jSON输出验证', () => {
    it('should generate valid JSON output', () => {
      @Controller({ tags: ['JSON测试'] })
      class JsonController {
        @RouteConfig({
          method: 'get',
          path: '/json',
        })
        async jsonTest() {}
      }

      const { json, object } = generateSwaggerDoc(getRegistry(), [
        JsonController,
      ])

      // 验证JSON是有效的
      expect(() => JSON.parse(json)).not.toThrow()

      // 验证JSON和object是一致的
      expect(JSON.parse(json)).toEqual(object)

      // 验证基本结构
      const parsed = JSON.parse(json)
      expect(parsed).toHaveProperty('openapi')
      expect(parsed).toHaveProperty('info')
      expect(parsed).toHaveProperty('paths')
      expect(parsed).toHaveProperty('components')
    })

    it('should handle special characters in paths and descriptions', () => {
      @Controller({ tags: ['特殊字符测试'] })
      class SpecialCharController {
        @RouteConfig({
          method: 'get',
          path: '/special-chars/{id}',
          summary: '特殊字符测试: <>"\'&',
          description: '包含特殊字符的描述\n换行符\t制表符',
        })
        async specialChars() {}
      }

      const { json, object } = generateSwaggerDoc(getRegistry(), [
        SpecialCharController,
      ])

      expect(() => JSON.parse(json)).not.toThrow()
      expect(object.paths['/special-chars/{id}'].get.summary).toBe(
        '特殊字符测试: <>"\'&',
      )
      expect(object.paths['/special-chars/{id}'].get.description).toBe(
        '包含特殊字符的描述\n换行符\t制表符',
      )
    })
  })
})
